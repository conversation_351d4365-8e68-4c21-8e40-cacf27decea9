pub mod mdq
pub mod mdq::md_elem
pub mod mdq::md_elem::elem
pub enum mdq::md_elem::elem::CodeVariant
pub mdq::md_elem::elem::CodeVariant::Code(core::option::Option<mdq::md_elem::elem::CodeOpts>)
pub mdq::md_elem::elem::CodeVariant::Math
pub mdq::md_elem::elem::CodeVariant::Math::metadata: core::option::Option<alloc::string::String>
impl core::clone::Clone for mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::clone(&self) -> mdq::md_elem::elem::CodeVariant
impl core::cmp::Eq for mdq::md_elem::elem::CodeVariant
impl core::cmp::PartialEq for mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::eq(&self, other: &mdq::md_elem::elem::CodeVariant) -> bool
impl core::fmt::Debug for mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::CodeVariant
impl core::marker::Freeze for mdq::md_elem::elem::CodeVariant
impl core::marker::Send for mdq::md_elem::elem::CodeVariant
impl core::marker::Sync for mdq::md_elem::elem::CodeVariant
impl core::marker::Unpin for mdq::md_elem::elem::CodeVariant
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::CodeVariant
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::CodeVariant
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::CodeVariant where U: core::convert::From<T>
pub fn mdq::md_elem::elem::CodeVariant::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::CodeVariant where U: core::convert::Into<T>
pub type mdq::md_elem::elem::CodeVariant::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::CodeVariant::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::CodeVariant where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::CodeVariant::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::CodeVariant::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::CodeVariant where T: core::clone::Clone
pub type mdq::md_elem::elem::CodeVariant::Owned = T
pub fn mdq::md_elem::elem::CodeVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::CodeVariant::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::CodeVariant where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::CodeVariant::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::CodeVariant where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::CodeVariant::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::CodeVariant where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::CodeVariant::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::CodeVariant where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::CodeVariant::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::from(t: T) -> T
pub enum mdq::md_elem::elem::ColumnAlignment
pub mdq::md_elem::elem::ColumnAlignment::Center
pub mdq::md_elem::elem::ColumnAlignment::Left
pub mdq::md_elem::elem::ColumnAlignment::Right
impl core::clone::Clone for mdq::md_elem::elem::ColumnAlignment
pub fn mdq::md_elem::elem::ColumnAlignment::clone(&self) -> mdq::md_elem::elem::ColumnAlignment
impl core::cmp::Eq for mdq::md_elem::elem::ColumnAlignment
impl core::cmp::Ord for mdq::md_elem::elem::ColumnAlignment
pub fn mdq::md_elem::elem::ColumnAlignment::cmp(&self, other: &mdq::md_elem::elem::ColumnAlignment) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::md_elem::elem::ColumnAlignment
pub fn mdq::md_elem::elem::ColumnAlignment::eq(&self, other: &mdq::md_elem::elem::ColumnAlignment) -> bool
impl core::cmp::PartialOrd for mdq::md_elem::elem::ColumnAlignment
pub fn mdq::md_elem::elem::ColumnAlignment::partial_cmp(&self, other: &mdq::md_elem::elem::ColumnAlignment) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::md_elem::elem::ColumnAlignment
pub fn mdq::md_elem::elem::ColumnAlignment::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::ColumnAlignment
pub fn mdq::md_elem::elem::ColumnAlignment::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::md_elem::elem::ColumnAlignment
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::ColumnAlignment
impl core::marker::Freeze for mdq::md_elem::elem::ColumnAlignment
impl core::marker::Send for mdq::md_elem::elem::ColumnAlignment
impl core::marker::Sync for mdq::md_elem::elem::ColumnAlignment
impl core::marker::Unpin for mdq::md_elem::elem::ColumnAlignment
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::ColumnAlignment
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::ColumnAlignment
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::ColumnAlignment where U: core::convert::From<T>
pub fn mdq::md_elem::elem::ColumnAlignment::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::ColumnAlignment where U: core::convert::Into<T>
pub type mdq::md_elem::elem::ColumnAlignment::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::ColumnAlignment::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::ColumnAlignment where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::ColumnAlignment::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::ColumnAlignment::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::ColumnAlignment where T: core::clone::Clone
pub type mdq::md_elem::elem::ColumnAlignment::Owned = T
pub fn mdq::md_elem::elem::ColumnAlignment::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::ColumnAlignment::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::ColumnAlignment where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::ColumnAlignment::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::ColumnAlignment where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::ColumnAlignment::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::ColumnAlignment where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::ColumnAlignment::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::ColumnAlignment where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::ColumnAlignment::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::ColumnAlignment
pub fn mdq::md_elem::elem::ColumnAlignment::from(t: T) -> T
impl<T> pest::RuleType for mdq::md_elem::elem::ColumnAlignment where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub enum mdq::md_elem::elem::FrontMatterVariant
pub mdq::md_elem::elem::FrontMatterVariant::Toml
pub mdq::md_elem::elem::FrontMatterVariant::Yaml
impl mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::name(self) -> &'static str
pub fn mdq::md_elem::elem::FrontMatterVariant::separator(self) -> &'static str
impl core::clone::Clone for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::clone(&self) -> mdq::md_elem::elem::FrontMatterVariant
impl core::cmp::Eq for mdq::md_elem::elem::FrontMatterVariant
impl core::cmp::Ord for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::cmp(&self, other: &mdq::md_elem::elem::FrontMatterVariant) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::eq(&self, other: &mdq::md_elem::elem::FrontMatterVariant) -> bool
impl core::cmp::PartialOrd for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::partial_cmp(&self, other: &mdq::md_elem::elem::FrontMatterVariant) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::md_elem::elem::FrontMatterVariant
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::FrontMatterVariant
impl core::marker::Freeze for mdq::md_elem::elem::FrontMatterVariant
impl core::marker::Send for mdq::md_elem::elem::FrontMatterVariant
impl core::marker::Sync for mdq::md_elem::elem::FrontMatterVariant
impl core::marker::Unpin for mdq::md_elem::elem::FrontMatterVariant
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::FrontMatterVariant
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::FrontMatterVariant
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::FrontMatterVariant where U: core::convert::From<T>
pub fn mdq::md_elem::elem::FrontMatterVariant::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::FrontMatterVariant where U: core::convert::Into<T>
pub type mdq::md_elem::elem::FrontMatterVariant::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::FrontMatterVariant::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::FrontMatterVariant where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::FrontMatterVariant::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::FrontMatterVariant::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::FrontMatterVariant where T: core::clone::Clone
pub type mdq::md_elem::elem::FrontMatterVariant::Owned = T
pub fn mdq::md_elem::elem::FrontMatterVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::FrontMatterVariant::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::FrontMatterVariant where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::FrontMatterVariant::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::FrontMatterVariant where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::FrontMatterVariant::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::FrontMatterVariant where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::FrontMatterVariant::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::FrontMatterVariant where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::FrontMatterVariant::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::from(t: T) -> T
impl<T> pest::RuleType for mdq::md_elem::elem::FrontMatterVariant where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub enum mdq::md_elem::elem::Inline
pub mdq::md_elem::elem::Inline::Footnote(mdq::md_elem::elem::FootnoteId)
pub mdq::md_elem::elem::Inline::Image(mdq::md_elem::elem::Image)
pub mdq::md_elem::elem::Inline::Link(mdq::md_elem::elem::Link)
pub mdq::md_elem::elem::Inline::Span(mdq::md_elem::elem::Span)
pub mdq::md_elem::elem::Inline::Text(mdq::md_elem::elem::Text)
impl core::clone::Clone for mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::clone(&self) -> mdq::md_elem::elem::Inline
impl core::cmp::Eq for mdq::md_elem::elem::Inline
impl core::cmp::PartialEq for mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::eq(&self, other: &mdq::md_elem::elem::Inline) -> bool
impl core::convert::From<mdq::md_elem::elem::Inline> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Inline) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::Inline
impl core::marker::Freeze for mdq::md_elem::elem::Inline
impl core::marker::Send for mdq::md_elem::elem::Inline
impl core::marker::Sync for mdq::md_elem::elem::Inline
impl core::marker::Unpin for mdq::md_elem::elem::Inline
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::Inline
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::Inline
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::Inline where U: core::convert::From<T>
pub fn mdq::md_elem::elem::Inline::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::Inline where U: core::convert::Into<T>
pub type mdq::md_elem::elem::Inline::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Inline::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::Inline where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::Inline::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Inline::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::Inline where T: core::clone::Clone
pub type mdq::md_elem::elem::Inline::Owned = T
pub fn mdq::md_elem::elem::Inline::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Inline::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::Inline where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::Inline::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::Inline where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Inline::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::Inline where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Inline::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::Inline where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::Inline::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::from(t: T) -> T
pub enum mdq::md_elem::elem::LinkReference
pub mdq::md_elem::elem::LinkReference::Collapsed
pub mdq::md_elem::elem::LinkReference::Full(alloc::string::String)
pub mdq::md_elem::elem::LinkReference::Inline
pub mdq::md_elem::elem::LinkReference::Shortcut
impl core::clone::Clone for mdq::md_elem::elem::LinkReference
pub fn mdq::md_elem::elem::LinkReference::clone(&self) -> mdq::md_elem::elem::LinkReference
impl core::cmp::Eq for mdq::md_elem::elem::LinkReference
impl core::cmp::Ord for mdq::md_elem::elem::LinkReference
pub fn mdq::md_elem::elem::LinkReference::cmp(&self, other: &mdq::md_elem::elem::LinkReference) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::md_elem::elem::LinkReference
pub fn mdq::md_elem::elem::LinkReference::eq(&self, other: &mdq::md_elem::elem::LinkReference) -> bool
impl core::cmp::PartialOrd for mdq::md_elem::elem::LinkReference
pub fn mdq::md_elem::elem::LinkReference::partial_cmp(&self, other: &mdq::md_elem::elem::LinkReference) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::md_elem::elem::LinkReference
pub fn mdq::md_elem::elem::LinkReference::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::LinkReference
pub fn mdq::md_elem::elem::LinkReference::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::LinkReference
impl core::marker::Freeze for mdq::md_elem::elem::LinkReference
impl core::marker::Send for mdq::md_elem::elem::LinkReference
impl core::marker::Sync for mdq::md_elem::elem::LinkReference
impl core::marker::Unpin for mdq::md_elem::elem::LinkReference
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::LinkReference
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::LinkReference
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::LinkReference where U: core::convert::From<T>
pub fn mdq::md_elem::elem::LinkReference::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::LinkReference where U: core::convert::Into<T>
pub type mdq::md_elem::elem::LinkReference::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::LinkReference::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::LinkReference where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::LinkReference::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::LinkReference::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::LinkReference where T: core::clone::Clone
pub type mdq::md_elem::elem::LinkReference::Owned = T
pub fn mdq::md_elem::elem::LinkReference::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::LinkReference::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::LinkReference where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::LinkReference::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::LinkReference where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::LinkReference::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::LinkReference where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::LinkReference::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::LinkReference where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::LinkReference::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::LinkReference
pub fn mdq::md_elem::elem::LinkReference::from(t: T) -> T
pub enum mdq::md_elem::elem::SpanVariant
pub mdq::md_elem::elem::SpanVariant::Delete
pub mdq::md_elem::elem::SpanVariant::Emphasis
pub mdq::md_elem::elem::SpanVariant::Strong
impl core::clone::Clone for mdq::md_elem::elem::SpanVariant
pub fn mdq::md_elem::elem::SpanVariant::clone(&self) -> mdq::md_elem::elem::SpanVariant
impl core::cmp::Eq for mdq::md_elem::elem::SpanVariant
impl core::cmp::Ord for mdq::md_elem::elem::SpanVariant
pub fn mdq::md_elem::elem::SpanVariant::cmp(&self, other: &mdq::md_elem::elem::SpanVariant) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::md_elem::elem::SpanVariant
pub fn mdq::md_elem::elem::SpanVariant::eq(&self, other: &mdq::md_elem::elem::SpanVariant) -> bool
impl core::cmp::PartialOrd for mdq::md_elem::elem::SpanVariant
pub fn mdq::md_elem::elem::SpanVariant::partial_cmp(&self, other: &mdq::md_elem::elem::SpanVariant) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::md_elem::elem::SpanVariant
pub fn mdq::md_elem::elem::SpanVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::SpanVariant
pub fn mdq::md_elem::elem::SpanVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::md_elem::elem::SpanVariant
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::SpanVariant
impl core::marker::Freeze for mdq::md_elem::elem::SpanVariant
impl core::marker::Send for mdq::md_elem::elem::SpanVariant
impl core::marker::Sync for mdq::md_elem::elem::SpanVariant
impl core::marker::Unpin for mdq::md_elem::elem::SpanVariant
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::SpanVariant
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::SpanVariant
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::SpanVariant where U: core::convert::From<T>
pub fn mdq::md_elem::elem::SpanVariant::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::SpanVariant where U: core::convert::Into<T>
pub type mdq::md_elem::elem::SpanVariant::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::SpanVariant::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::SpanVariant where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::SpanVariant::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::SpanVariant::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::SpanVariant where T: core::clone::Clone
pub type mdq::md_elem::elem::SpanVariant::Owned = T
pub fn mdq::md_elem::elem::SpanVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::SpanVariant::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::SpanVariant where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::SpanVariant::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::SpanVariant where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::SpanVariant::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::SpanVariant where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::SpanVariant::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::SpanVariant where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::SpanVariant::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::SpanVariant
pub fn mdq::md_elem::elem::SpanVariant::from(t: T) -> T
impl<T> pest::RuleType for mdq::md_elem::elem::SpanVariant where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub enum mdq::md_elem::elem::TextVariant
pub mdq::md_elem::elem::TextVariant::Code
pub mdq::md_elem::elem::TextVariant::InlineHtml
pub mdq::md_elem::elem::TextVariant::Math
pub mdq::md_elem::elem::TextVariant::Plain
impl core::clone::Clone for mdq::md_elem::elem::TextVariant
pub fn mdq::md_elem::elem::TextVariant::clone(&self) -> mdq::md_elem::elem::TextVariant
impl core::cmp::Eq for mdq::md_elem::elem::TextVariant
impl core::cmp::Ord for mdq::md_elem::elem::TextVariant
pub fn mdq::md_elem::elem::TextVariant::cmp(&self, other: &mdq::md_elem::elem::TextVariant) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::md_elem::elem::TextVariant
pub fn mdq::md_elem::elem::TextVariant::eq(&self, other: &mdq::md_elem::elem::TextVariant) -> bool
impl core::cmp::PartialOrd for mdq::md_elem::elem::TextVariant
pub fn mdq::md_elem::elem::TextVariant::partial_cmp(&self, other: &mdq::md_elem::elem::TextVariant) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::md_elem::elem::TextVariant
pub fn mdq::md_elem::elem::TextVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::TextVariant
pub fn mdq::md_elem::elem::TextVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::md_elem::elem::TextVariant
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::TextVariant
impl core::marker::Freeze for mdq::md_elem::elem::TextVariant
impl core::marker::Send for mdq::md_elem::elem::TextVariant
impl core::marker::Sync for mdq::md_elem::elem::TextVariant
impl core::marker::Unpin for mdq::md_elem::elem::TextVariant
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::TextVariant
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::TextVariant
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::TextVariant where U: core::convert::From<T>
pub fn mdq::md_elem::elem::TextVariant::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::TextVariant where U: core::convert::Into<T>
pub type mdq::md_elem::elem::TextVariant::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::TextVariant::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::TextVariant where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::TextVariant::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::TextVariant::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::TextVariant where T: core::clone::Clone
pub type mdq::md_elem::elem::TextVariant::Owned = T
pub fn mdq::md_elem::elem::TextVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::TextVariant::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::TextVariant where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::TextVariant::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::TextVariant where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::TextVariant::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::TextVariant where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::TextVariant::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::TextVariant where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::TextVariant::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::TextVariant
pub fn mdq::md_elem::elem::TextVariant::from(t: T) -> T
impl<T> pest::RuleType for mdq::md_elem::elem::TextVariant where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub struct mdq::md_elem::elem::BlockHtml
pub mdq::md_elem::elem::BlockHtml::value: alloc::string::String
impl core::clone::Clone for mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::clone(&self) -> mdq::md_elem::elem::BlockHtml
impl core::cmp::Eq for mdq::md_elem::elem::BlockHtml
impl core::cmp::PartialEq for mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::eq(&self, other: &mdq::md_elem::elem::BlockHtml) -> bool
impl core::convert::From<alloc::string::String> for mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::from(value: alloc::string::String) -> Self
impl core::convert::From<mdq::md_elem::elem::BlockHtml> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockHtml) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::BlockHtml
impl core::marker::Freeze for mdq::md_elem::elem::BlockHtml
impl core::marker::Send for mdq::md_elem::elem::BlockHtml
impl core::marker::Sync for mdq::md_elem::elem::BlockHtml
impl core::marker::Unpin for mdq::md_elem::elem::BlockHtml
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::BlockHtml
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::BlockHtml
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::BlockHtml where U: core::convert::From<T>
pub fn mdq::md_elem::elem::BlockHtml::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::BlockHtml where U: core::convert::Into<T>
pub type mdq::md_elem::elem::BlockHtml::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::BlockHtml::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::BlockHtml where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::BlockHtml::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::BlockHtml::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::BlockHtml where T: core::clone::Clone
pub type mdq::md_elem::elem::BlockHtml::Owned = T
pub fn mdq::md_elem::elem::BlockHtml::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::BlockHtml::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::BlockHtml where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::BlockHtml::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::BlockHtml where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::BlockHtml::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::BlockHtml where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::BlockHtml::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::BlockHtml where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::BlockHtml::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::from(t: T) -> T
pub struct mdq::md_elem::elem::BlockQuote
pub mdq::md_elem::elem::BlockQuote::body: alloc::vec::Vec<mdq::md_elem::MdElem>
impl core::clone::Clone for mdq::md_elem::elem::BlockQuote
pub fn mdq::md_elem::elem::BlockQuote::clone(&self) -> mdq::md_elem::elem::BlockQuote
impl core::cmp::Eq for mdq::md_elem::elem::BlockQuote
impl core::cmp::PartialEq for mdq::md_elem::elem::BlockQuote
pub fn mdq::md_elem::elem::BlockQuote::eq(&self, other: &mdq::md_elem::elem::BlockQuote) -> bool
impl core::convert::From<mdq::md_elem::elem::BlockQuote> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockQuote) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::BlockQuote
pub fn mdq::md_elem::elem::BlockQuote::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::BlockQuote
pub fn mdq::md_elem::elem::BlockQuote::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::BlockQuote
impl core::marker::Freeze for mdq::md_elem::elem::BlockQuote
impl core::marker::Send for mdq::md_elem::elem::BlockQuote
impl core::marker::Sync for mdq::md_elem::elem::BlockQuote
impl core::marker::Unpin for mdq::md_elem::elem::BlockQuote
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::BlockQuote
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::BlockQuote
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::BlockQuote where U: core::convert::From<T>
pub fn mdq::md_elem::elem::BlockQuote::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::BlockQuote where U: core::convert::Into<T>
pub type mdq::md_elem::elem::BlockQuote::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::BlockQuote::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::BlockQuote where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::BlockQuote::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::BlockQuote::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::BlockQuote where T: core::clone::Clone
pub type mdq::md_elem::elem::BlockQuote::Owned = T
pub fn mdq::md_elem::elem::BlockQuote::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::BlockQuote::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::BlockQuote where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::BlockQuote::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::BlockQuote where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::BlockQuote::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::BlockQuote where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::BlockQuote::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::BlockQuote where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::BlockQuote::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::BlockQuote
pub fn mdq::md_elem::elem::BlockQuote::from(t: T) -> T
pub struct mdq::md_elem::elem::CodeBlock
pub mdq::md_elem::elem::CodeBlock::value: alloc::string::String
pub mdq::md_elem::elem::CodeBlock::variant: mdq::md_elem::elem::CodeVariant
impl core::clone::Clone for mdq::md_elem::elem::CodeBlock
pub fn mdq::md_elem::elem::CodeBlock::clone(&self) -> mdq::md_elem::elem::CodeBlock
impl core::cmp::Eq for mdq::md_elem::elem::CodeBlock
impl core::cmp::PartialEq for mdq::md_elem::elem::CodeBlock
pub fn mdq::md_elem::elem::CodeBlock::eq(&self, other: &mdq::md_elem::elem::CodeBlock) -> bool
impl core::convert::From<mdq::md_elem::elem::CodeBlock> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::CodeBlock) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::CodeBlock
pub fn mdq::md_elem::elem::CodeBlock::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::CodeBlock
pub fn mdq::md_elem::elem::CodeBlock::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::CodeBlock
impl core::marker::Freeze for mdq::md_elem::elem::CodeBlock
impl core::marker::Send for mdq::md_elem::elem::CodeBlock
impl core::marker::Sync for mdq::md_elem::elem::CodeBlock
impl core::marker::Unpin for mdq::md_elem::elem::CodeBlock
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::CodeBlock
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::CodeBlock
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::CodeBlock where U: core::convert::From<T>
pub fn mdq::md_elem::elem::CodeBlock::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::CodeBlock where U: core::convert::Into<T>
pub type mdq::md_elem::elem::CodeBlock::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::CodeBlock::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::CodeBlock where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::CodeBlock::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::CodeBlock::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::CodeBlock where T: core::clone::Clone
pub type mdq::md_elem::elem::CodeBlock::Owned = T
pub fn mdq::md_elem::elem::CodeBlock::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::CodeBlock::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::CodeBlock where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::CodeBlock::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::CodeBlock where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::CodeBlock::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::CodeBlock where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::CodeBlock::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::CodeBlock where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::CodeBlock::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::CodeBlock
pub fn mdq::md_elem::elem::CodeBlock::from(t: T) -> T
pub struct mdq::md_elem::elem::CodeOpts
pub mdq::md_elem::elem::CodeOpts::language: alloc::string::String
pub mdq::md_elem::elem::CodeOpts::metadata: core::option::Option<alloc::string::String>
impl core::clone::Clone for mdq::md_elem::elem::CodeOpts
pub fn mdq::md_elem::elem::CodeOpts::clone(&self) -> mdq::md_elem::elem::CodeOpts
impl core::cmp::Eq for mdq::md_elem::elem::CodeOpts
impl core::cmp::PartialEq for mdq::md_elem::elem::CodeOpts
pub fn mdq::md_elem::elem::CodeOpts::eq(&self, other: &mdq::md_elem::elem::CodeOpts) -> bool
impl core::fmt::Debug for mdq::md_elem::elem::CodeOpts
pub fn mdq::md_elem::elem::CodeOpts::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::CodeOpts
pub fn mdq::md_elem::elem::CodeOpts::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::CodeOpts
impl core::marker::Freeze for mdq::md_elem::elem::CodeOpts
impl core::marker::Send for mdq::md_elem::elem::CodeOpts
impl core::marker::Sync for mdq::md_elem::elem::CodeOpts
impl core::marker::Unpin for mdq::md_elem::elem::CodeOpts
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::CodeOpts
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::CodeOpts
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::CodeOpts where U: core::convert::From<T>
pub fn mdq::md_elem::elem::CodeOpts::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::CodeOpts where U: core::convert::Into<T>
pub type mdq::md_elem::elem::CodeOpts::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::CodeOpts::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::CodeOpts where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::CodeOpts::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::CodeOpts::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::CodeOpts where T: core::clone::Clone
pub type mdq::md_elem::elem::CodeOpts::Owned = T
pub fn mdq::md_elem::elem::CodeOpts::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::CodeOpts::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::CodeOpts where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::CodeOpts::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::CodeOpts where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::CodeOpts::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::CodeOpts where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::CodeOpts::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::CodeOpts where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::CodeOpts::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::CodeOpts
pub fn mdq::md_elem::elem::CodeOpts::from(t: T) -> T
pub struct mdq::md_elem::elem::FootnoteId
pub mdq::md_elem::elem::FootnoteId::id: alloc::string::String
impl mdq::md_elem::elem::FootnoteId
pub fn mdq::md_elem::elem::FootnoteId::as_str(&self) -> &str
impl core::clone::Clone for mdq::md_elem::elem::FootnoteId
pub fn mdq::md_elem::elem::FootnoteId::clone(&self) -> mdq::md_elem::elem::FootnoteId
impl core::cmp::Eq for mdq::md_elem::elem::FootnoteId
impl core::cmp::PartialEq for mdq::md_elem::elem::FootnoteId
pub fn mdq::md_elem::elem::FootnoteId::eq(&self, other: &mdq::md_elem::elem::FootnoteId) -> bool
impl core::convert::From<alloc::string::String> for mdq::md_elem::elem::FootnoteId
pub fn mdq::md_elem::elem::FootnoteId::from(id: alloc::string::String) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::FootnoteId
pub fn mdq::md_elem::elem::FootnoteId::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::FootnoteId
pub fn mdq::md_elem::elem::FootnoteId::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::FootnoteId
impl core::marker::Freeze for mdq::md_elem::elem::FootnoteId
impl core::marker::Send for mdq::md_elem::elem::FootnoteId
impl core::marker::Sync for mdq::md_elem::elem::FootnoteId
impl core::marker::Unpin for mdq::md_elem::elem::FootnoteId
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::FootnoteId
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::FootnoteId
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::FootnoteId where U: core::convert::From<T>
pub fn mdq::md_elem::elem::FootnoteId::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::FootnoteId where U: core::convert::Into<T>
pub type mdq::md_elem::elem::FootnoteId::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::FootnoteId::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::FootnoteId where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::FootnoteId::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::FootnoteId::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::FootnoteId where T: core::clone::Clone
pub type mdq::md_elem::elem::FootnoteId::Owned = T
pub fn mdq::md_elem::elem::FootnoteId::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::FootnoteId::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::FootnoteId where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::FootnoteId::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::FootnoteId where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::FootnoteId::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::FootnoteId where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::FootnoteId::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::FootnoteId where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::FootnoteId::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::FootnoteId
pub fn mdq::md_elem::elem::FootnoteId::from(t: T) -> T
pub struct mdq::md_elem::elem::FrontMatter
pub mdq::md_elem::elem::FrontMatter::body: alloc::string::String
pub mdq::md_elem::elem::FrontMatter::variant: mdq::md_elem::elem::FrontMatterVariant
impl core::clone::Clone for mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::clone(&self) -> mdq::md_elem::elem::FrontMatter
impl core::cmp::Eq for mdq::md_elem::elem::FrontMatter
impl core::cmp::PartialEq for mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::eq(&self, other: &mdq::md_elem::elem::FrontMatter) -> bool
impl core::convert::From<mdq::md_elem::elem::FrontMatter> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::FrontMatter) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::FrontMatter
impl core::marker::Freeze for mdq::md_elem::elem::FrontMatter
impl core::marker::Send for mdq::md_elem::elem::FrontMatter
impl core::marker::Sync for mdq::md_elem::elem::FrontMatter
impl core::marker::Unpin for mdq::md_elem::elem::FrontMatter
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::FrontMatter
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::FrontMatter
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::FrontMatter where U: core::convert::From<T>
pub fn mdq::md_elem::elem::FrontMatter::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::FrontMatter where U: core::convert::Into<T>
pub type mdq::md_elem::elem::FrontMatter::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::FrontMatter::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::FrontMatter where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::FrontMatter::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::FrontMatter::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::FrontMatter where T: core::clone::Clone
pub type mdq::md_elem::elem::FrontMatter::Owned = T
pub fn mdq::md_elem::elem::FrontMatter::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::FrontMatter::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::FrontMatter where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::FrontMatter::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::FrontMatter where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::FrontMatter::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::FrontMatter where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::FrontMatter::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::FrontMatter where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::FrontMatter::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::from(t: T) -> T
pub struct mdq::md_elem::elem::Image
pub mdq::md_elem::elem::Image::alt: alloc::string::String
pub mdq::md_elem::elem::Image::link: mdq::md_elem::elem::LinkDefinition
impl core::clone::Clone for mdq::md_elem::elem::Image
pub fn mdq::md_elem::elem::Image::clone(&self) -> mdq::md_elem::elem::Image
impl core::cmp::Eq for mdq::md_elem::elem::Image
impl core::cmp::PartialEq for mdq::md_elem::elem::Image
pub fn mdq::md_elem::elem::Image::eq(&self, other: &mdq::md_elem::elem::Image) -> bool
impl core::convert::From<mdq::md_elem::elem::Image> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Image) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::Image
pub fn mdq::md_elem::elem::Image::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::Image
pub fn mdq::md_elem::elem::Image::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::Image
impl core::marker::Freeze for mdq::md_elem::elem::Image
impl core::marker::Send for mdq::md_elem::elem::Image
impl core::marker::Sync for mdq::md_elem::elem::Image
impl core::marker::Unpin for mdq::md_elem::elem::Image
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::Image
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::Image
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::Image where U: core::convert::From<T>
pub fn mdq::md_elem::elem::Image::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::Image where U: core::convert::Into<T>
pub type mdq::md_elem::elem::Image::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Image::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::Image where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::Image::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Image::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::Image where T: core::clone::Clone
pub type mdq::md_elem::elem::Image::Owned = T
pub fn mdq::md_elem::elem::Image::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Image::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::Image where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::Image::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::Image where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Image::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::Image where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Image::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::Image where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::Image::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::Image
pub fn mdq::md_elem::elem::Image::from(t: T) -> T
pub struct mdq::md_elem::elem::Link
pub mdq::md_elem::elem::Link::display: alloc::vec::Vec<mdq::md_elem::elem::Inline>
pub mdq::md_elem::elem::Link::link: mdq::md_elem::elem::LinkDefinition
impl core::clone::Clone for mdq::md_elem::elem::Link
pub fn mdq::md_elem::elem::Link::clone(&self) -> mdq::md_elem::elem::Link
impl core::cmp::Eq for mdq::md_elem::elem::Link
impl core::cmp::PartialEq for mdq::md_elem::elem::Link
pub fn mdq::md_elem::elem::Link::eq(&self, other: &mdq::md_elem::elem::Link) -> bool
impl core::convert::From<mdq::md_elem::elem::Link> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Link) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::Link
pub fn mdq::md_elem::elem::Link::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::Link
pub fn mdq::md_elem::elem::Link::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::Link
impl core::marker::Freeze for mdq::md_elem::elem::Link
impl core::marker::Send for mdq::md_elem::elem::Link
impl core::marker::Sync for mdq::md_elem::elem::Link
impl core::marker::Unpin for mdq::md_elem::elem::Link
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::Link
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::Link
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::Link where U: core::convert::From<T>
pub fn mdq::md_elem::elem::Link::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::Link where U: core::convert::Into<T>
pub type mdq::md_elem::elem::Link::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Link::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::Link where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::Link::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Link::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::Link where T: core::clone::Clone
pub type mdq::md_elem::elem::Link::Owned = T
pub fn mdq::md_elem::elem::Link::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Link::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::Link where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::Link::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::Link where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Link::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::Link where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Link::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::Link where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::Link::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::Link
pub fn mdq::md_elem::elem::Link::from(t: T) -> T
pub struct mdq::md_elem::elem::LinkDefinition
pub mdq::md_elem::elem::LinkDefinition::reference: mdq::md_elem::elem::LinkReference
pub mdq::md_elem::elem::LinkDefinition::title: core::option::Option<alloc::string::String>
pub mdq::md_elem::elem::LinkDefinition::url: alloc::string::String
impl core::clone::Clone for mdq::md_elem::elem::LinkDefinition
pub fn mdq::md_elem::elem::LinkDefinition::clone(&self) -> mdq::md_elem::elem::LinkDefinition
impl core::cmp::Eq for mdq::md_elem::elem::LinkDefinition
impl core::cmp::PartialEq for mdq::md_elem::elem::LinkDefinition
pub fn mdq::md_elem::elem::LinkDefinition::eq(&self, other: &mdq::md_elem::elem::LinkDefinition) -> bool
impl core::fmt::Debug for mdq::md_elem::elem::LinkDefinition
pub fn mdq::md_elem::elem::LinkDefinition::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::LinkDefinition
pub fn mdq::md_elem::elem::LinkDefinition::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::LinkDefinition
impl core::marker::Freeze for mdq::md_elem::elem::LinkDefinition
impl core::marker::Send for mdq::md_elem::elem::LinkDefinition
impl core::marker::Sync for mdq::md_elem::elem::LinkDefinition
impl core::marker::Unpin for mdq::md_elem::elem::LinkDefinition
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::LinkDefinition
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::LinkDefinition
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::LinkDefinition where U: core::convert::From<T>
pub fn mdq::md_elem::elem::LinkDefinition::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::LinkDefinition where U: core::convert::Into<T>
pub type mdq::md_elem::elem::LinkDefinition::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::LinkDefinition::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::LinkDefinition where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::LinkDefinition::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::LinkDefinition::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::LinkDefinition where T: core::clone::Clone
pub type mdq::md_elem::elem::LinkDefinition::Owned = T
pub fn mdq::md_elem::elem::LinkDefinition::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::LinkDefinition::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::LinkDefinition where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::LinkDefinition::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::LinkDefinition where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::LinkDefinition::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::LinkDefinition where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::LinkDefinition::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::LinkDefinition where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::LinkDefinition::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::LinkDefinition
pub fn mdq::md_elem::elem::LinkDefinition::from(t: T) -> T
pub struct mdq::md_elem::elem::List
pub mdq::md_elem::elem::List::items: alloc::vec::Vec<mdq::md_elem::elem::ListItem>
pub mdq::md_elem::elem::List::starting_index: core::option::Option<u32>
impl core::clone::Clone for mdq::md_elem::elem::List
pub fn mdq::md_elem::elem::List::clone(&self) -> mdq::md_elem::elem::List
impl core::cmp::Eq for mdq::md_elem::elem::List
impl core::cmp::PartialEq for mdq::md_elem::elem::List
pub fn mdq::md_elem::elem::List::eq(&self, other: &mdq::md_elem::elem::List) -> bool
impl core::convert::From<mdq::md_elem::elem::List> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::List) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::List
pub fn mdq::md_elem::elem::List::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::List
pub fn mdq::md_elem::elem::List::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::List
impl core::marker::Freeze for mdq::md_elem::elem::List
impl core::marker::Send for mdq::md_elem::elem::List
impl core::marker::Sync for mdq::md_elem::elem::List
impl core::marker::Unpin for mdq::md_elem::elem::List
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::List
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::List
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::List where U: core::convert::From<T>
pub fn mdq::md_elem::elem::List::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::List where U: core::convert::Into<T>
pub type mdq::md_elem::elem::List::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::List::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::List where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::List::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::List::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::List where T: core::clone::Clone
pub type mdq::md_elem::elem::List::Owned = T
pub fn mdq::md_elem::elem::List::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::List::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::List where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::List::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::List where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::List::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::List where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::List::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::List where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::List::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::List
pub fn mdq::md_elem::elem::List::from(t: T) -> T
pub struct mdq::md_elem::elem::ListItem
pub mdq::md_elem::elem::ListItem::checked: core::option::Option<bool>
pub mdq::md_elem::elem::ListItem::item: alloc::vec::Vec<mdq::md_elem::MdElem>
impl core::clone::Clone for mdq::md_elem::elem::ListItem
pub fn mdq::md_elem::elem::ListItem::clone(&self) -> mdq::md_elem::elem::ListItem
impl core::cmp::Eq for mdq::md_elem::elem::ListItem
impl core::cmp::PartialEq for mdq::md_elem::elem::ListItem
pub fn mdq::md_elem::elem::ListItem::eq(&self, other: &mdq::md_elem::elem::ListItem) -> bool
impl core::fmt::Debug for mdq::md_elem::elem::ListItem
pub fn mdq::md_elem::elem::ListItem::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::ListItem
pub fn mdq::md_elem::elem::ListItem::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::ListItem
impl core::marker::Freeze for mdq::md_elem::elem::ListItem
impl core::marker::Send for mdq::md_elem::elem::ListItem
impl core::marker::Sync for mdq::md_elem::elem::ListItem
impl core::marker::Unpin for mdq::md_elem::elem::ListItem
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::ListItem
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::ListItem
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::ListItem where U: core::convert::From<T>
pub fn mdq::md_elem::elem::ListItem::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::ListItem where U: core::convert::Into<T>
pub type mdq::md_elem::elem::ListItem::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::ListItem::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::ListItem where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::ListItem::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::ListItem::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::ListItem where T: core::clone::Clone
pub type mdq::md_elem::elem::ListItem::Owned = T
pub fn mdq::md_elem::elem::ListItem::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::ListItem::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::ListItem where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::ListItem::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::ListItem where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::ListItem::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::ListItem where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::ListItem::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::ListItem where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::ListItem::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::ListItem
pub fn mdq::md_elem::elem::ListItem::from(t: T) -> T
pub struct mdq::md_elem::elem::Paragraph
pub mdq::md_elem::elem::Paragraph::body: alloc::vec::Vec<mdq::md_elem::elem::Inline>
impl core::clone::Clone for mdq::md_elem::elem::Paragraph
pub fn mdq::md_elem::elem::Paragraph::clone(&self) -> mdq::md_elem::elem::Paragraph
impl core::cmp::Eq for mdq::md_elem::elem::Paragraph
impl core::cmp::PartialEq for mdq::md_elem::elem::Paragraph
pub fn mdq::md_elem::elem::Paragraph::eq(&self, other: &mdq::md_elem::elem::Paragraph) -> bool
impl core::convert::From<mdq::md_elem::elem::Paragraph> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Paragraph) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::Paragraph
pub fn mdq::md_elem::elem::Paragraph::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::Paragraph
pub fn mdq::md_elem::elem::Paragraph::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::Paragraph
impl core::marker::Freeze for mdq::md_elem::elem::Paragraph
impl core::marker::Send for mdq::md_elem::elem::Paragraph
impl core::marker::Sync for mdq::md_elem::elem::Paragraph
impl core::marker::Unpin for mdq::md_elem::elem::Paragraph
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::Paragraph
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::Paragraph
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::Paragraph where U: core::convert::From<T>
pub fn mdq::md_elem::elem::Paragraph::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::Paragraph where U: core::convert::Into<T>
pub type mdq::md_elem::elem::Paragraph::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Paragraph::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::Paragraph where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::Paragraph::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Paragraph::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::Paragraph where T: core::clone::Clone
pub type mdq::md_elem::elem::Paragraph::Owned = T
pub fn mdq::md_elem::elem::Paragraph::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Paragraph::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::Paragraph where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::Paragraph::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::Paragraph where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Paragraph::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::Paragraph where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Paragraph::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::Paragraph where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::Paragraph::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::Paragraph
pub fn mdq::md_elem::elem::Paragraph::from(t: T) -> T
pub struct mdq::md_elem::elem::Section
pub mdq::md_elem::elem::Section::body: alloc::vec::Vec<mdq::md_elem::MdElem>
pub mdq::md_elem::elem::Section::depth: u8
pub mdq::md_elem::elem::Section::title: alloc::vec::Vec<mdq::md_elem::elem::Inline>
impl core::clone::Clone for mdq::md_elem::elem::Section
pub fn mdq::md_elem::elem::Section::clone(&self) -> mdq::md_elem::elem::Section
impl core::cmp::Eq for mdq::md_elem::elem::Section
impl core::cmp::PartialEq for mdq::md_elem::elem::Section
pub fn mdq::md_elem::elem::Section::eq(&self, other: &mdq::md_elem::elem::Section) -> bool
impl core::convert::From<mdq::md_elem::elem::Section> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Section) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::Section
pub fn mdq::md_elem::elem::Section::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::Section
pub fn mdq::md_elem::elem::Section::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::Section
impl core::marker::Freeze for mdq::md_elem::elem::Section
impl core::marker::Send for mdq::md_elem::elem::Section
impl core::marker::Sync for mdq::md_elem::elem::Section
impl core::marker::Unpin for mdq::md_elem::elem::Section
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::Section
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::Section
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::Section where U: core::convert::From<T>
pub fn mdq::md_elem::elem::Section::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::Section where U: core::convert::Into<T>
pub type mdq::md_elem::elem::Section::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Section::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::Section where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::Section::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Section::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::Section where T: core::clone::Clone
pub type mdq::md_elem::elem::Section::Owned = T
pub fn mdq::md_elem::elem::Section::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Section::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::Section where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::Section::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::Section where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Section::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::Section where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Section::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::Section where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::Section::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::Section
pub fn mdq::md_elem::elem::Section::from(t: T) -> T
pub struct mdq::md_elem::elem::Span
pub mdq::md_elem::elem::Span::children: alloc::vec::Vec<mdq::md_elem::elem::Inline>
pub mdq::md_elem::elem::Span::variant: mdq::md_elem::elem::SpanVariant
impl core::clone::Clone for mdq::md_elem::elem::Span
pub fn mdq::md_elem::elem::Span::clone(&self) -> mdq::md_elem::elem::Span
impl core::cmp::Eq for mdq::md_elem::elem::Span
impl core::cmp::PartialEq for mdq::md_elem::elem::Span
pub fn mdq::md_elem::elem::Span::eq(&self, other: &mdq::md_elem::elem::Span) -> bool
impl core::fmt::Debug for mdq::md_elem::elem::Span
pub fn mdq::md_elem::elem::Span::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::Span
pub fn mdq::md_elem::elem::Span::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::Span
impl core::marker::Freeze for mdq::md_elem::elem::Span
impl core::marker::Send for mdq::md_elem::elem::Span
impl core::marker::Sync for mdq::md_elem::elem::Span
impl core::marker::Unpin for mdq::md_elem::elem::Span
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::Span
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::Span
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::Span where U: core::convert::From<T>
pub fn mdq::md_elem::elem::Span::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::Span where U: core::convert::Into<T>
pub type mdq::md_elem::elem::Span::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Span::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::Span where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::Span::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Span::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::Span where T: core::clone::Clone
pub type mdq::md_elem::elem::Span::Owned = T
pub fn mdq::md_elem::elem::Span::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Span::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::Span where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::Span::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::Span where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Span::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::Span where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Span::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::Span where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::Span::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::Span
pub fn mdq::md_elem::elem::Span::from(t: T) -> T
pub struct mdq::md_elem::elem::Table
pub mdq::md_elem::elem::Table::alignments: alloc::vec::Vec<core::option::Option<mdq::md_elem::elem::ColumnAlignment>>
pub mdq::md_elem::elem::Table::rows: alloc::vec::Vec<mdq::md_elem::elem::TableRow>
impl mdq::md_elem::elem::Table
pub fn mdq::md_elem::elem::Table::alignments(&self) -> &[core::option::Option<mdq::md_elem::elem::ColumnAlignment>]
pub fn mdq::md_elem::elem::Table::is_empty(&self) -> bool
pub fn mdq::md_elem::elem::Table::normalize(&mut self)
pub fn mdq::md_elem::elem::Table::retain_columns_by_header<F, E>(&mut self, f: F) -> core::result::Result<(), E> where F: core::ops::function::FnMut(&mdq::md_elem::elem::TableCell) -> core::result::Result<bool, E>
pub fn mdq::md_elem::elem::Table::retain_rows<F, E>(&mut self, f: F) -> core::result::Result<(), E> where F: core::ops::function::FnMut(&mdq::md_elem::elem::TableCell) -> core::result::Result<bool, E>
pub fn mdq::md_elem::elem::Table::rows(&self) -> &alloc::vec::Vec<mdq::md_elem::elem::TableRow>
impl core::clone::Clone for mdq::md_elem::elem::Table
pub fn mdq::md_elem::elem::Table::clone(&self) -> mdq::md_elem::elem::Table
impl core::cmp::Eq for mdq::md_elem::elem::Table
impl core::cmp::PartialEq for mdq::md_elem::elem::Table
pub fn mdq::md_elem::elem::Table::eq(&self, other: &mdq::md_elem::elem::Table) -> bool
impl core::convert::From<mdq::md_elem::elem::Table> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Table) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::Table
pub fn mdq::md_elem::elem::Table::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::Table
pub fn mdq::md_elem::elem::Table::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::Table
impl core::marker::Freeze for mdq::md_elem::elem::Table
impl core::marker::Send for mdq::md_elem::elem::Table
impl core::marker::Sync for mdq::md_elem::elem::Table
impl core::marker::Unpin for mdq::md_elem::elem::Table
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::Table
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::Table
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::Table where U: core::convert::From<T>
pub fn mdq::md_elem::elem::Table::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::Table where U: core::convert::Into<T>
pub type mdq::md_elem::elem::Table::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Table::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::Table where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::Table::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Table::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::Table where T: core::clone::Clone
pub type mdq::md_elem::elem::Table::Owned = T
pub fn mdq::md_elem::elem::Table::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Table::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::Table where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::Table::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::Table where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Table::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::Table where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Table::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::Table where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::Table::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::Table
pub fn mdq::md_elem::elem::Table::from(t: T) -> T
pub struct mdq::md_elem::elem::Text
pub mdq::md_elem::elem::Text::value: alloc::string::String
pub mdq::md_elem::elem::Text::variant: mdq::md_elem::elem::TextVariant
impl core::clone::Clone for mdq::md_elem::elem::Text
pub fn mdq::md_elem::elem::Text::clone(&self) -> mdq::md_elem::elem::Text
impl core::cmp::Eq for mdq::md_elem::elem::Text
impl core::cmp::PartialEq for mdq::md_elem::elem::Text
pub fn mdq::md_elem::elem::Text::eq(&self, other: &mdq::md_elem::elem::Text) -> bool
impl core::fmt::Debug for mdq::md_elem::elem::Text
pub fn mdq::md_elem::elem::Text::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::Text
pub fn mdq::md_elem::elem::Text::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::Text
impl core::marker::Freeze for mdq::md_elem::elem::Text
impl core::marker::Send for mdq::md_elem::elem::Text
impl core::marker::Sync for mdq::md_elem::elem::Text
impl core::marker::Unpin for mdq::md_elem::elem::Text
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::Text
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::Text
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::Text where U: core::convert::From<T>
pub fn mdq::md_elem::elem::Text::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::Text where U: core::convert::Into<T>
pub type mdq::md_elem::elem::Text::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Text::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::Text where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::Text::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Text::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::Text where T: core::clone::Clone
pub type mdq::md_elem::elem::Text::Owned = T
pub fn mdq::md_elem::elem::Text::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Text::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::Text where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::Text::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::Text where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Text::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::Text where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Text::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::Text where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::Text::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::Text
pub fn mdq::md_elem::elem::Text::from(t: T) -> T
pub type mdq::md_elem::elem::TableCell = alloc::vec::Vec<mdq::md_elem::elem::Inline>
pub type mdq::md_elem::elem::TableRow = alloc::vec::Vec<mdq::md_elem::elem::TableCell>
pub enum mdq::md_elem::InvalidMd
pub mdq::md_elem::InvalidMd::ConflictingReferenceDefinition(alloc::string::String)
pub mdq::md_elem::InvalidMd::InternalError(mdq::md_elem::UnknownMdParseError)
pub mdq::md_elem::InvalidMd::MissingReferenceDefinition(alloc::string::String)
pub mdq::md_elem::InvalidMd::NonInlineWhereInlineExpected(mdq::md_elem::MdElem)
pub mdq::md_elem::InvalidMd::NonListItemDirectlyUnderList(mdq::md_elem::MarkdownPart)
pub mdq::md_elem::InvalidMd::NonRowDirectlyUnderTable(mdq::md_elem::MarkdownPart)
pub mdq::md_elem::InvalidMd::ParseError(alloc::string::String)
pub mdq::md_elem::InvalidMd::UnknownMarkdown(&'static str)
pub mdq::md_elem::InvalidMd::Unsupported(mdq::md_elem::MarkdownPart)
impl core::cmp::Eq for mdq::md_elem::InvalidMd
impl core::cmp::PartialEq for mdq::md_elem::InvalidMd
pub fn mdq::md_elem::InvalidMd::eq(&self, other: &mdq::md_elem::InvalidMd) -> bool
impl core::error::Error for mdq::md_elem::InvalidMd
impl core::fmt::Debug for mdq::md_elem::InvalidMd
pub fn mdq::md_elem::InvalidMd::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::md_elem::InvalidMd
pub fn mdq::md_elem::InvalidMd::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::StructuralPartialEq for mdq::md_elem::InvalidMd
impl !core::marker::Freeze for mdq::md_elem::InvalidMd
impl core::marker::Send for mdq::md_elem::InvalidMd
impl core::marker::Sync for mdq::md_elem::InvalidMd
impl core::marker::Unpin for mdq::md_elem::InvalidMd
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::InvalidMd
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::InvalidMd
impl<T, U> core::convert::Into<U> for mdq::md_elem::InvalidMd where U: core::convert::From<T>
pub fn mdq::md_elem::InvalidMd::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::InvalidMd where U: core::convert::Into<T>
pub type mdq::md_elem::InvalidMd::Error = core::convert::Infallible
pub fn mdq::md_elem::InvalidMd::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::InvalidMd where U: core::convert::TryFrom<T>
pub type mdq::md_elem::InvalidMd::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::InvalidMd::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::string::ToString for mdq::md_elem::InvalidMd where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::md_elem::InvalidMd::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::md_elem::InvalidMd where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::InvalidMd::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::InvalidMd where T: ?core::marker::Sized
pub fn mdq::md_elem::InvalidMd::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::InvalidMd where T: ?core::marker::Sized
pub fn mdq::md_elem::InvalidMd::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::md_elem::InvalidMd
pub fn mdq::md_elem::InvalidMd::from(t: T) -> T
pub enum mdq::md_elem::MdElem
pub mdq::md_elem::MdElem::BlockHtml(mdq::md_elem::elem::BlockHtml)
pub mdq::md_elem::MdElem::BlockQuote(mdq::md_elem::elem::BlockQuote)
pub mdq::md_elem::MdElem::CodeBlock(mdq::md_elem::elem::CodeBlock)
pub mdq::md_elem::MdElem::Doc(alloc::vec::Vec<mdq::md_elem::MdElem>)
pub mdq::md_elem::MdElem::FrontMatter(mdq::md_elem::elem::FrontMatter)
pub mdq::md_elem::MdElem::Inline(mdq::md_elem::elem::Inline)
pub mdq::md_elem::MdElem::List(mdq::md_elem::elem::List)
pub mdq::md_elem::MdElem::Paragraph(mdq::md_elem::elem::Paragraph)
pub mdq::md_elem::MdElem::Section(mdq::md_elem::elem::Section)
pub mdq::md_elem::MdElem::Table(mdq::md_elem::elem::Table)
pub mdq::md_elem::MdElem::ThematicBreak
impl core::clone::Clone for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::clone(&self) -> mdq::md_elem::MdElem
impl core::cmp::Eq for mdq::md_elem::MdElem
impl core::cmp::PartialEq for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::eq(&self, other: &mdq::md_elem::MdElem) -> bool
impl core::convert::From<alloc::vec::Vec<mdq::md_elem::MdElem>> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(elems: alloc::vec::Vec<mdq::md_elem::MdElem>) -> Self
impl core::convert::From<mdq::md_elem::elem::BlockHtml> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockHtml) -> Self
impl core::convert::From<mdq::md_elem::elem::BlockQuote> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockQuote) -> Self
impl core::convert::From<mdq::md_elem::elem::CodeBlock> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::CodeBlock) -> Self
impl core::convert::From<mdq::md_elem::elem::FrontMatter> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::FrontMatter) -> Self
impl core::convert::From<mdq::md_elem::elem::Image> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Image) -> Self
impl core::convert::From<mdq::md_elem::elem::Inline> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Inline) -> Self
impl core::convert::From<mdq::md_elem::elem::Link> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Link) -> Self
impl core::convert::From<mdq::md_elem::elem::List> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::List) -> Self
impl core::convert::From<mdq::md_elem::elem::Paragraph> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Paragraph) -> Self
impl core::convert::From<mdq::md_elem::elem::Section> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Section) -> Self
impl core::convert::From<mdq::md_elem::elem::Table> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Table) -> Self
impl core::fmt::Debug for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::MdElem
impl core::marker::Freeze for mdq::md_elem::MdElem
impl core::marker::Send for mdq::md_elem::MdElem
impl core::marker::Sync for mdq::md_elem::MdElem
impl core::marker::Unpin for mdq::md_elem::MdElem
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::MdElem
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::MdElem
impl<T, U> core::convert::Into<U> for mdq::md_elem::MdElem where U: core::convert::From<T>
pub fn mdq::md_elem::MdElem::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::MdElem where U: core::convert::Into<T>
pub type mdq::md_elem::MdElem::Error = core::convert::Infallible
pub fn mdq::md_elem::MdElem::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::MdElem where U: core::convert::TryFrom<T>
pub type mdq::md_elem::MdElem::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::MdElem::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::MdElem where T: core::clone::Clone
pub type mdq::md_elem::MdElem::Owned = T
pub fn mdq::md_elem::MdElem::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MdElem::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::MdElem where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::MdElem::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::MdElem where T: ?core::marker::Sized
pub fn mdq::md_elem::MdElem::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::MdElem where T: ?core::marker::Sized
pub fn mdq::md_elem::MdElem::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::MdElem where T: core::clone::Clone
pub unsafe fn mdq::md_elem::MdElem::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(t: T) -> T
pub struct mdq::md_elem::MarkdownPart
impl core::clone::Clone for mdq::md_elem::MarkdownPart
pub fn mdq::md_elem::MarkdownPart::clone(&self) -> mdq::md_elem::MarkdownPart
impl core::cmp::Eq for mdq::md_elem::MarkdownPart
impl core::cmp::PartialEq for mdq::md_elem::MarkdownPart
pub fn mdq::md_elem::MarkdownPart::eq(&self, other: &mdq::md_elem::MarkdownPart) -> bool
impl core::fmt::Debug for mdq::md_elem::MarkdownPart
pub fn mdq::md_elem::MarkdownPart::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::StructuralPartialEq for mdq::md_elem::MarkdownPart
impl core::marker::Freeze for mdq::md_elem::MarkdownPart
impl core::marker::Send for mdq::md_elem::MarkdownPart
impl core::marker::Sync for mdq::md_elem::MarkdownPart
impl core::marker::Unpin for mdq::md_elem::MarkdownPart
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::MarkdownPart
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::MarkdownPart
impl<T, U> core::convert::Into<U> for mdq::md_elem::MarkdownPart where U: core::convert::From<T>
pub fn mdq::md_elem::MarkdownPart::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::MarkdownPart where U: core::convert::Into<T>
pub type mdq::md_elem::MarkdownPart::Error = core::convert::Infallible
pub fn mdq::md_elem::MarkdownPart::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::MarkdownPart where U: core::convert::TryFrom<T>
pub type mdq::md_elem::MarkdownPart::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::MarkdownPart::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::MarkdownPart where T: core::clone::Clone
pub type mdq::md_elem::MarkdownPart::Owned = T
pub fn mdq::md_elem::MarkdownPart::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MarkdownPart::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::MarkdownPart where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::MarkdownPart::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::MarkdownPart where T: ?core::marker::Sized
pub fn mdq::md_elem::MarkdownPart::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::MarkdownPart where T: ?core::marker::Sized
pub fn mdq::md_elem::MarkdownPart::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::MarkdownPart where T: core::clone::Clone
pub unsafe fn mdq::md_elem::MarkdownPart::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::MarkdownPart
pub fn mdq::md_elem::MarkdownPart::from(t: T) -> T
pub struct mdq::md_elem::MdContext
impl mdq::md_elem::MdContext
pub fn mdq::md_elem::MdContext::get_footnote(&self, footnote_id: &mdq::md_elem::elem::FootnoteId) -> &alloc::vec::Vec<mdq::md_elem::MdElem>
impl core::clone::Clone for mdq::md_elem::MdContext
pub fn mdq::md_elem::MdContext::clone(&self) -> mdq::md_elem::MdContext
impl core::cmp::PartialEq for mdq::md_elem::MdContext
pub fn mdq::md_elem::MdContext::eq(&self, other: &mdq::md_elem::MdContext) -> bool
impl core::default::Default for mdq::md_elem::MdContext
pub fn mdq::md_elem::MdContext::default() -> mdq::md_elem::MdContext
impl core::fmt::Debug for mdq::md_elem::MdContext
pub fn mdq::md_elem::MdContext::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::StructuralPartialEq for mdq::md_elem::MdContext
impl core::marker::Freeze for mdq::md_elem::MdContext
impl core::marker::Send for mdq::md_elem::MdContext
impl core::marker::Sync for mdq::md_elem::MdContext
impl core::marker::Unpin for mdq::md_elem::MdContext
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::MdContext
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::MdContext
impl<T, U> core::convert::Into<U> for mdq::md_elem::MdContext where U: core::convert::From<T>
pub fn mdq::md_elem::MdContext::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::MdContext where U: core::convert::Into<T>
pub type mdq::md_elem::MdContext::Error = core::convert::Infallible
pub fn mdq::md_elem::MdContext::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::MdContext where U: core::convert::TryFrom<T>
pub type mdq::md_elem::MdContext::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::MdContext::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::MdContext where T: core::clone::Clone
pub type mdq::md_elem::MdContext::Owned = T
pub fn mdq::md_elem::MdContext::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MdContext::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::MdContext where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::MdContext::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::MdContext where T: ?core::marker::Sized
pub fn mdq::md_elem::MdContext::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::MdContext where T: ?core::marker::Sized
pub fn mdq::md_elem::MdContext::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::MdContext where T: core::clone::Clone
pub unsafe fn mdq::md_elem::MdContext::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::MdContext
pub fn mdq::md_elem::MdContext::from(t: T) -> T
pub struct mdq::md_elem::MdDoc
pub mdq::md_elem::MdDoc::ctx: mdq::md_elem::MdContext
pub mdq::md_elem::MdDoc::roots: alloc::vec::Vec<mdq::md_elem::MdElem>
impl mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::parse(text: &str, options: &mdq::md_elem::ParseOptions) -> core::result::Result<Self, mdq::md_elem::InvalidMd>
impl core::clone::Clone for mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::clone(&self) -> mdq::md_elem::MdDoc
impl core::cmp::PartialEq for mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::eq(&self, other: &mdq::md_elem::MdDoc) -> bool
impl core::default::Default for mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::default() -> mdq::md_elem::MdDoc
impl core::fmt::Debug for mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::StructuralPartialEq for mdq::md_elem::MdDoc
impl core::marker::Freeze for mdq::md_elem::MdDoc
impl core::marker::Send for mdq::md_elem::MdDoc
impl core::marker::Sync for mdq::md_elem::MdDoc
impl core::marker::Unpin for mdq::md_elem::MdDoc
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::MdDoc
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::MdDoc
impl<T, U> core::convert::Into<U> for mdq::md_elem::MdDoc where U: core::convert::From<T>
pub fn mdq::md_elem::MdDoc::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::MdDoc where U: core::convert::Into<T>
pub type mdq::md_elem::MdDoc::Error = core::convert::Infallible
pub fn mdq::md_elem::MdDoc::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::MdDoc where U: core::convert::TryFrom<T>
pub type mdq::md_elem::MdDoc::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::MdDoc::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::MdDoc where T: core::clone::Clone
pub type mdq::md_elem::MdDoc::Owned = T
pub fn mdq::md_elem::MdDoc::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MdDoc::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::MdDoc where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::MdDoc::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::MdDoc where T: ?core::marker::Sized
pub fn mdq::md_elem::MdDoc::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::MdDoc where T: ?core::marker::Sized
pub fn mdq::md_elem::MdDoc::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::MdDoc where T: core::clone::Clone
pub unsafe fn mdq::md_elem::MdDoc::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::from(t: T) -> T
pub struct mdq::md_elem::ParseOptions
pub mdq::md_elem::ParseOptions::allow_unknown_markdown: bool
impl mdq::md_elem::ParseOptions
pub fn mdq::md_elem::ParseOptions::gfm() -> Self
impl core::default::Default for mdq::md_elem::ParseOptions
pub fn mdq::md_elem::ParseOptions::default() -> Self
impl core::fmt::Debug for mdq::md_elem::ParseOptions
pub fn mdq::md_elem::ParseOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::Freeze for mdq::md_elem::ParseOptions
impl !core::marker::Send for mdq::md_elem::ParseOptions
impl !core::marker::Sync for mdq::md_elem::ParseOptions
impl core::marker::Unpin for mdq::md_elem::ParseOptions
impl !core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::ParseOptions
impl !core::panic::unwind_safe::UnwindSafe for mdq::md_elem::ParseOptions
impl<T, U> core::convert::Into<U> for mdq::md_elem::ParseOptions where U: core::convert::From<T>
pub fn mdq::md_elem::ParseOptions::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::ParseOptions where U: core::convert::Into<T>
pub type mdq::md_elem::ParseOptions::Error = core::convert::Infallible
pub fn mdq::md_elem::ParseOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::ParseOptions where U: core::convert::TryFrom<T>
pub type mdq::md_elem::ParseOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::ParseOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> core::any::Any for mdq::md_elem::ParseOptions where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::ParseOptions::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::ParseOptions where T: ?core::marker::Sized
pub fn mdq::md_elem::ParseOptions::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::ParseOptions where T: ?core::marker::Sized
pub fn mdq::md_elem::ParseOptions::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::md_elem::ParseOptions
pub fn mdq::md_elem::ParseOptions::from(t: T) -> T
pub struct mdq::md_elem::UnknownMdParseError
impl core::cmp::Eq for mdq::md_elem::UnknownMdParseError
impl core::cmp::PartialEq for mdq::md_elem::UnknownMdParseError
pub fn mdq::md_elem::UnknownMdParseError::eq(&self, _other: &Self) -> bool
impl core::error::Error for mdq::md_elem::UnknownMdParseError
impl core::fmt::Debug for mdq::md_elem::UnknownMdParseError
pub fn mdq::md_elem::UnknownMdParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::md_elem::UnknownMdParseError
pub fn mdq::md_elem::UnknownMdParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl !core::marker::Freeze for mdq::md_elem::UnknownMdParseError
impl core::marker::Send for mdq::md_elem::UnknownMdParseError
impl core::marker::Sync for mdq::md_elem::UnknownMdParseError
impl core::marker::Unpin for mdq::md_elem::UnknownMdParseError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::UnknownMdParseError
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::UnknownMdParseError
impl<T, U> core::convert::Into<U> for mdq::md_elem::UnknownMdParseError where U: core::convert::From<T>
pub fn mdq::md_elem::UnknownMdParseError::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::UnknownMdParseError where U: core::convert::Into<T>
pub type mdq::md_elem::UnknownMdParseError::Error = core::convert::Infallible
pub fn mdq::md_elem::UnknownMdParseError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::UnknownMdParseError where U: core::convert::TryFrom<T>
pub type mdq::md_elem::UnknownMdParseError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::UnknownMdParseError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::string::ToString for mdq::md_elem::UnknownMdParseError where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::md_elem::UnknownMdParseError::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::md_elem::UnknownMdParseError where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::UnknownMdParseError::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::UnknownMdParseError where T: ?core::marker::Sized
pub fn mdq::md_elem::UnknownMdParseError::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::UnknownMdParseError where T: ?core::marker::Sized
pub fn mdq::md_elem::UnknownMdParseError::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::md_elem::UnknownMdParseError
pub fn mdq::md_elem::UnknownMdParseError::from(t: T) -> T
pub mod mdq::output
#[non_exhaustive] pub enum mdq::output::InlineElemOptionsBuilderError
pub mdq::output::InlineElemOptionsBuilderError::UninitializedField(&'static str)
pub mdq::output::InlineElemOptionsBuilderError::ValidationError(alloc::string::String)
impl core::convert::From<alloc::string::String> for mdq::output::InlineElemOptionsBuilderError
pub fn mdq::output::InlineElemOptionsBuilderError::from(s: alloc::string::String) -> Self
impl core::convert::From<derive_builder::error::UninitializedFieldError> for mdq::output::InlineElemOptionsBuilderError
pub fn mdq::output::InlineElemOptionsBuilderError::from(s: derive_builder::error::UninitializedFieldError) -> Self
impl core::error::Error for mdq::output::InlineElemOptionsBuilderError
impl core::fmt::Debug for mdq::output::InlineElemOptionsBuilderError
pub fn mdq::output::InlineElemOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::output::InlineElemOptionsBuilderError
pub fn mdq::output::InlineElemOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::Freeze for mdq::output::InlineElemOptionsBuilderError
impl core::marker::Send for mdq::output::InlineElemOptionsBuilderError
impl core::marker::Sync for mdq::output::InlineElemOptionsBuilderError
impl core::marker::Unpin for mdq::output::InlineElemOptionsBuilderError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::InlineElemOptionsBuilderError
impl core::panic::unwind_safe::UnwindSafe for mdq::output::InlineElemOptionsBuilderError
impl<T, U> core::convert::Into<U> for mdq::output::InlineElemOptionsBuilderError where U: core::convert::From<T>
pub fn mdq::output::InlineElemOptionsBuilderError::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::InlineElemOptionsBuilderError where U: core::convert::Into<T>
pub type mdq::output::InlineElemOptionsBuilderError::Error = core::convert::Infallible
pub fn mdq::output::InlineElemOptionsBuilderError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::InlineElemOptionsBuilderError where U: core::convert::TryFrom<T>
pub type mdq::output::InlineElemOptionsBuilderError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::InlineElemOptionsBuilderError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::string::ToString for mdq::output::InlineElemOptionsBuilderError where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::output::InlineElemOptionsBuilderError::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::output::InlineElemOptionsBuilderError where T: 'static + ?core::marker::Sized
pub fn mdq::output::InlineElemOptionsBuilderError::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::InlineElemOptionsBuilderError where T: ?core::marker::Sized
pub fn mdq::output::InlineElemOptionsBuilderError::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::InlineElemOptionsBuilderError where T: ?core::marker::Sized
pub fn mdq::output::InlineElemOptionsBuilderError::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::output::InlineElemOptionsBuilderError
pub fn mdq::output::InlineElemOptionsBuilderError::from(t: T) -> T
pub enum mdq::output::LinkTransform
pub mdq::output::LinkTransform::Inline
pub mdq::output::LinkTransform::Keep
pub mdq::output::LinkTransform::Reference
impl clap_builder::derive::ValueEnum for mdq::output::LinkTransform
pub fn mdq::output::LinkTransform::to_possible_value<'a>(&self) -> core::option::Option<clap_builder::builder::possible_value::PossibleValue>
pub fn mdq::output::LinkTransform::value_variants<'a>() -> &'a [Self]
impl core::clone::Clone for mdq::output::LinkTransform
pub fn mdq::output::LinkTransform::clone(&self) -> mdq::output::LinkTransform
impl core::cmp::Eq for mdq::output::LinkTransform
impl core::cmp::Ord for mdq::output::LinkTransform
pub fn mdq::output::LinkTransform::cmp(&self, other: &mdq::output::LinkTransform) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::output::LinkTransform
pub fn mdq::output::LinkTransform::eq(&self, other: &mdq::output::LinkTransform) -> bool
impl core::cmp::PartialOrd for mdq::output::LinkTransform
pub fn mdq::output::LinkTransform::partial_cmp(&self, other: &mdq::output::LinkTransform) -> core::option::Option<core::cmp::Ordering>
impl core::default::Default for mdq::output::LinkTransform
pub fn mdq::output::LinkTransform::default() -> mdq::output::LinkTransform
impl core::fmt::Debug for mdq::output::LinkTransform
pub fn mdq::output::LinkTransform::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::output::LinkTransform
pub fn mdq::output::LinkTransform::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::output::LinkTransform
impl core::marker::StructuralPartialEq for mdq::output::LinkTransform
impl core::marker::Freeze for mdq::output::LinkTransform
impl core::marker::Send for mdq::output::LinkTransform
impl core::marker::Sync for mdq::output::LinkTransform
impl core::marker::Unpin for mdq::output::LinkTransform
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::LinkTransform
impl core::panic::unwind_safe::UnwindSafe for mdq::output::LinkTransform
impl<T, U> core::convert::Into<U> for mdq::output::LinkTransform where U: core::convert::From<T>
pub fn mdq::output::LinkTransform::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::LinkTransform where U: core::convert::Into<T>
pub type mdq::output::LinkTransform::Error = core::convert::Infallible
pub fn mdq::output::LinkTransform::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::LinkTransform where U: core::convert::TryFrom<T>
pub type mdq::output::LinkTransform::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::LinkTransform::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::LinkTransform where T: core::clone::Clone
pub type mdq::output::LinkTransform::Owned = T
pub fn mdq::output::LinkTransform::clone_into(&self, target: &mut T)
pub fn mdq::output::LinkTransform::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::LinkTransform where T: 'static + ?core::marker::Sized
pub fn mdq::output::LinkTransform::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::LinkTransform where T: ?core::marker::Sized
pub fn mdq::output::LinkTransform::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::LinkTransform where T: ?core::marker::Sized
pub fn mdq::output::LinkTransform::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::LinkTransform where T: core::clone::Clone
pub unsafe fn mdq::output::LinkTransform::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::LinkTransform
pub fn mdq::output::LinkTransform::from(t: T) -> T
impl<T> pest::RuleType for mdq::output::LinkTransform where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
#[non_exhaustive] pub enum mdq::output::MdWriterOptionsBuilderError
pub mdq::output::MdWriterOptionsBuilderError::UninitializedField(&'static str)
pub mdq::output::MdWriterOptionsBuilderError::ValidationError(alloc::string::String)
impl core::convert::From<alloc::string::String> for mdq::output::MdWriterOptionsBuilderError
pub fn mdq::output::MdWriterOptionsBuilderError::from(s: alloc::string::String) -> Self
impl core::convert::From<derive_builder::error::UninitializedFieldError> for mdq::output::MdWriterOptionsBuilderError
pub fn mdq::output::MdWriterOptionsBuilderError::from(s: derive_builder::error::UninitializedFieldError) -> Self
impl core::error::Error for mdq::output::MdWriterOptionsBuilderError
impl core::fmt::Debug for mdq::output::MdWriterOptionsBuilderError
pub fn mdq::output::MdWriterOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::output::MdWriterOptionsBuilderError
pub fn mdq::output::MdWriterOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::Freeze for mdq::output::MdWriterOptionsBuilderError
impl core::marker::Send for mdq::output::MdWriterOptionsBuilderError
impl core::marker::Sync for mdq::output::MdWriterOptionsBuilderError
impl core::marker::Unpin for mdq::output::MdWriterOptionsBuilderError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::MdWriterOptionsBuilderError
impl core::panic::unwind_safe::UnwindSafe for mdq::output::MdWriterOptionsBuilderError
impl<T, U> core::convert::Into<U> for mdq::output::MdWriterOptionsBuilderError where U: core::convert::From<T>
pub fn mdq::output::MdWriterOptionsBuilderError::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::MdWriterOptionsBuilderError where U: core::convert::Into<T>
pub type mdq::output::MdWriterOptionsBuilderError::Error = core::convert::Infallible
pub fn mdq::output::MdWriterOptionsBuilderError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::MdWriterOptionsBuilderError where U: core::convert::TryFrom<T>
pub type mdq::output::MdWriterOptionsBuilderError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::MdWriterOptionsBuilderError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::string::ToString for mdq::output::MdWriterOptionsBuilderError where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilderError::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::output::MdWriterOptionsBuilderError where T: 'static + ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilderError::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::MdWriterOptionsBuilderError where T: ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilderError::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::MdWriterOptionsBuilderError where T: ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilderError::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::output::MdWriterOptionsBuilderError
pub fn mdq::output::MdWriterOptionsBuilderError::from(t: T) -> T
pub enum mdq::output::ReferencePlacement
pub mdq::output::ReferencePlacement::Doc
pub mdq::output::ReferencePlacement::Section
impl clap_builder::derive::ValueEnum for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::to_possible_value<'a>(&self) -> core::option::Option<clap_builder::builder::possible_value::PossibleValue>
pub fn mdq::output::ReferencePlacement::value_variants<'a>() -> &'a [Self]
impl core::clone::Clone for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::clone(&self) -> mdq::output::ReferencePlacement
impl core::cmp::Eq for mdq::output::ReferencePlacement
impl core::cmp::Ord for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::cmp(&self, other: &mdq::output::ReferencePlacement) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::eq(&self, other: &mdq::output::ReferencePlacement) -> bool
impl core::cmp::PartialOrd for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::partial_cmp(&self, other: &mdq::output::ReferencePlacement) -> core::option::Option<core::cmp::Ordering>
impl core::default::Default for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::default() -> mdq::output::ReferencePlacement
impl core::fmt::Debug for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::output::ReferencePlacement
impl core::marker::StructuralPartialEq for mdq::output::ReferencePlacement
impl core::marker::Freeze for mdq::output::ReferencePlacement
impl core::marker::Send for mdq::output::ReferencePlacement
impl core::marker::Sync for mdq::output::ReferencePlacement
impl core::marker::Unpin for mdq::output::ReferencePlacement
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::ReferencePlacement
impl core::panic::unwind_safe::UnwindSafe for mdq::output::ReferencePlacement
impl<T, U> core::convert::Into<U> for mdq::output::ReferencePlacement where U: core::convert::From<T>
pub fn mdq::output::ReferencePlacement::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::ReferencePlacement where U: core::convert::Into<T>
pub type mdq::output::ReferencePlacement::Error = core::convert::Infallible
pub fn mdq::output::ReferencePlacement::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::ReferencePlacement where U: core::convert::TryFrom<T>
pub type mdq::output::ReferencePlacement::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::ReferencePlacement::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::ReferencePlacement where T: core::clone::Clone
pub type mdq::output::ReferencePlacement::Owned = T
pub fn mdq::output::ReferencePlacement::clone_into(&self, target: &mut T)
pub fn mdq::output::ReferencePlacement::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::ReferencePlacement where T: 'static + ?core::marker::Sized
pub fn mdq::output::ReferencePlacement::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::ReferencePlacement where T: ?core::marker::Sized
pub fn mdq::output::ReferencePlacement::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::ReferencePlacement where T: ?core::marker::Sized
pub fn mdq::output::ReferencePlacement::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::ReferencePlacement where T: core::clone::Clone
pub unsafe fn mdq::output::ReferencePlacement::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::from(t: T) -> T
impl<T> pest::RuleType for mdq::output::ReferencePlacement where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub struct mdq::output::InlineElemOptions
pub mdq::output::InlineElemOptions::link_format: mdq::output::LinkTransform
pub mdq::output::InlineElemOptions::renumber_footnotes: bool
impl core::clone::Clone for mdq::output::InlineElemOptions
pub fn mdq::output::InlineElemOptions::clone(&self) -> mdq::output::InlineElemOptions
impl core::cmp::Eq for mdq::output::InlineElemOptions
impl core::cmp::Ord for mdq::output::InlineElemOptions
pub fn mdq::output::InlineElemOptions::cmp(&self, other: &mdq::output::InlineElemOptions) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::output::InlineElemOptions
pub fn mdq::output::InlineElemOptions::eq(&self, other: &mdq::output::InlineElemOptions) -> bool
impl core::cmp::PartialOrd for mdq::output::InlineElemOptions
pub fn mdq::output::InlineElemOptions::partial_cmp(&self, other: &mdq::output::InlineElemOptions) -> core::option::Option<core::cmp::Ordering>
impl core::default::Default for mdq::output::InlineElemOptions
pub fn mdq::output::InlineElemOptions::default() -> mdq::output::InlineElemOptions
impl core::fmt::Debug for mdq::output::InlineElemOptions
pub fn mdq::output::InlineElemOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::output::InlineElemOptions
pub fn mdq::output::InlineElemOptions::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::output::InlineElemOptions
impl core::marker::StructuralPartialEq for mdq::output::InlineElemOptions
impl core::marker::Freeze for mdq::output::InlineElemOptions
impl core::marker::Send for mdq::output::InlineElemOptions
impl core::marker::Sync for mdq::output::InlineElemOptions
impl core::marker::Unpin for mdq::output::InlineElemOptions
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::InlineElemOptions
impl core::panic::unwind_safe::UnwindSafe for mdq::output::InlineElemOptions
impl<T, U> core::convert::Into<U> for mdq::output::InlineElemOptions where U: core::convert::From<T>
pub fn mdq::output::InlineElemOptions::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::InlineElemOptions where U: core::convert::Into<T>
pub type mdq::output::InlineElemOptions::Error = core::convert::Infallible
pub fn mdq::output::InlineElemOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::InlineElemOptions where U: core::convert::TryFrom<T>
pub type mdq::output::InlineElemOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::InlineElemOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::InlineElemOptions where T: core::clone::Clone
pub type mdq::output::InlineElemOptions::Owned = T
pub fn mdq::output::InlineElemOptions::clone_into(&self, target: &mut T)
pub fn mdq::output::InlineElemOptions::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::InlineElemOptions where T: 'static + ?core::marker::Sized
pub fn mdq::output::InlineElemOptions::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::InlineElemOptions where T: ?core::marker::Sized
pub fn mdq::output::InlineElemOptions::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::InlineElemOptions where T: ?core::marker::Sized
pub fn mdq::output::InlineElemOptions::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::InlineElemOptions where T: core::clone::Clone
pub unsafe fn mdq::output::InlineElemOptions::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::InlineElemOptions
pub fn mdq::output::InlineElemOptions::from(t: T) -> T
impl<T> pest::RuleType for mdq::output::InlineElemOptions where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub struct mdq::output::InlineElemOptionsBuilder
impl mdq::output::InlineElemOptionsBuilder
pub fn mdq::output::InlineElemOptionsBuilder::build(&self) -> core::result::Result<mdq::output::InlineElemOptions, mdq::output::InlineElemOptionsBuilderError>
pub fn mdq::output::InlineElemOptionsBuilder::link_format(&mut self, value: mdq::output::LinkTransform) -> &mut Self
pub fn mdq::output::InlineElemOptionsBuilder::renumber_footnotes(&mut self, value: bool) -> &mut Self
impl core::clone::Clone for mdq::output::InlineElemOptionsBuilder
pub fn mdq::output::InlineElemOptionsBuilder::clone(&self) -> mdq::output::InlineElemOptionsBuilder
impl core::default::Default for mdq::output::InlineElemOptionsBuilder
pub fn mdq::output::InlineElemOptionsBuilder::default() -> Self
impl core::marker::Freeze for mdq::output::InlineElemOptionsBuilder
impl core::marker::Send for mdq::output::InlineElemOptionsBuilder
impl core::marker::Sync for mdq::output::InlineElemOptionsBuilder
impl core::marker::Unpin for mdq::output::InlineElemOptionsBuilder
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::InlineElemOptionsBuilder
impl core::panic::unwind_safe::UnwindSafe for mdq::output::InlineElemOptionsBuilder
impl<T, U> core::convert::Into<U> for mdq::output::InlineElemOptionsBuilder where U: core::convert::From<T>
pub fn mdq::output::InlineElemOptionsBuilder::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::InlineElemOptionsBuilder where U: core::convert::Into<T>
pub type mdq::output::InlineElemOptionsBuilder::Error = core::convert::Infallible
pub fn mdq::output::InlineElemOptionsBuilder::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::InlineElemOptionsBuilder where U: core::convert::TryFrom<T>
pub type mdq::output::InlineElemOptionsBuilder::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::InlineElemOptionsBuilder::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::InlineElemOptionsBuilder where T: core::clone::Clone
pub type mdq::output::InlineElemOptionsBuilder::Owned = T
pub fn mdq::output::InlineElemOptionsBuilder::clone_into(&self, target: &mut T)
pub fn mdq::output::InlineElemOptionsBuilder::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::InlineElemOptionsBuilder where T: 'static + ?core::marker::Sized
pub fn mdq::output::InlineElemOptionsBuilder::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::InlineElemOptionsBuilder where T: ?core::marker::Sized
pub fn mdq::output::InlineElemOptionsBuilder::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::InlineElemOptionsBuilder where T: ?core::marker::Sized
pub fn mdq::output::InlineElemOptionsBuilder::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::InlineElemOptionsBuilder where T: core::clone::Clone
pub unsafe fn mdq::output::InlineElemOptionsBuilder::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::InlineElemOptionsBuilder
pub fn mdq::output::InlineElemOptionsBuilder::from(t: T) -> T
pub struct mdq::output::IoAdapter<W>(pub W)
impl<W: core::clone::Clone> core::clone::Clone for mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::clone(&self) -> mdq::output::IoAdapter<W>
impl<W: core::cmp::Eq> core::cmp::Eq for mdq::output::IoAdapter<W>
impl<W: core::cmp::Ord> core::cmp::Ord for mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::cmp(&self, other: &mdq::output::IoAdapter<W>) -> core::cmp::Ordering
impl<W: core::cmp::PartialEq> core::cmp::PartialEq for mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::eq(&self, other: &mdq::output::IoAdapter<W>) -> bool
impl<W: core::cmp::PartialOrd> core::cmp::PartialOrd for mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::partial_cmp(&self, other: &mdq::output::IoAdapter<W>) -> core::option::Option<core::cmp::Ordering>
impl<W: core::default::Default> core::default::Default for mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::default() -> mdq::output::IoAdapter<W>
impl<W: core::fmt::Debug> core::fmt::Debug for mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl<W: core::hash::Hash> core::hash::Hash for mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl<W: core::marker::Copy> core::marker::Copy for mdq::output::IoAdapter<W>
impl<W: std::io::Write> core::fmt::Write for mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::write_str(&mut self, s: &str) -> core::fmt::Result
impl<W> core::convert::From<W> for mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::from(value: W) -> Self
impl<W> core::marker::StructuralPartialEq for mdq::output::IoAdapter<W>
impl<W> core::marker::Freeze for mdq::output::IoAdapter<W> where W: core::marker::Freeze
impl<W> core::marker::Send for mdq::output::IoAdapter<W> where W: core::marker::Send
impl<W> core::marker::Sync for mdq::output::IoAdapter<W> where W: core::marker::Sync
impl<W> core::marker::Unpin for mdq::output::IoAdapter<W> where W: core::marker::Unpin
impl<W> core::panic::unwind_safe::RefUnwindSafe for mdq::output::IoAdapter<W> where W: core::panic::unwind_safe::RefUnwindSafe
impl<W> core::panic::unwind_safe::UnwindSafe for mdq::output::IoAdapter<W> where W: core::panic::unwind_safe::UnwindSafe
impl<T, U> core::convert::Into<U> for mdq::output::IoAdapter<W> where U: core::convert::From<T>
pub fn mdq::output::IoAdapter<W>::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::IoAdapter<W> where U: core::convert::Into<T>
pub type mdq::output::IoAdapter<W>::Error = core::convert::Infallible
pub fn mdq::output::IoAdapter<W>::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::IoAdapter<W> where U: core::convert::TryFrom<T>
pub type mdq::output::IoAdapter<W>::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::IoAdapter<W>::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::IoAdapter<W> where T: core::clone::Clone
pub type mdq::output::IoAdapter<W>::Owned = T
pub fn mdq::output::IoAdapter<W>::clone_into(&self, target: &mut T)
pub fn mdq::output::IoAdapter<W>::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::IoAdapter<W> where T: 'static + ?core::marker::Sized
pub fn mdq::output::IoAdapter<W>::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::IoAdapter<W> where T: ?core::marker::Sized
pub fn mdq::output::IoAdapter<W>::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::IoAdapter<W> where T: ?core::marker::Sized
pub fn mdq::output::IoAdapter<W>::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::IoAdapter<W> where T: core::clone::Clone
pub unsafe fn mdq::output::IoAdapter<W>::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::from(t: T) -> T
impl<T> core::convert::From<never> for mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::from(t: never) -> T
impl<T> pest::RuleType for mdq::output::IoAdapter<W> where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub struct mdq::output::MdWriter
impl mdq::output::MdWriter
pub fn mdq::output::MdWriter::with_options(options: mdq::output::MdWriterOptions) -> Self
pub fn mdq::output::MdWriter::write<'md, I, W>(&self, ctx: &'md mdq::md_elem::MdContext, nodes: I, out: &mut W) where I: core::iter::traits::collect::IntoIterator<Item = &'md mdq::md_elem::MdElem>, W: core::fmt::Write
impl core::clone::Clone for mdq::output::MdWriter
pub fn mdq::output::MdWriter::clone(&self) -> mdq::output::MdWriter
impl core::cmp::Eq for mdq::output::MdWriter
impl core::cmp::Ord for mdq::output::MdWriter
pub fn mdq::output::MdWriter::cmp(&self, other: &mdq::output::MdWriter) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::output::MdWriter
pub fn mdq::output::MdWriter::eq(&self, other: &mdq::output::MdWriter) -> bool
impl core::cmp::PartialOrd for mdq::output::MdWriter
pub fn mdq::output::MdWriter::partial_cmp(&self, other: &mdq::output::MdWriter) -> core::option::Option<core::cmp::Ordering>
impl core::default::Default for mdq::output::MdWriter
pub fn mdq::output::MdWriter::default() -> mdq::output::MdWriter
impl core::fmt::Debug for mdq::output::MdWriter
pub fn mdq::output::MdWriter::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::output::MdWriter
pub fn mdq::output::MdWriter::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::output::MdWriter
impl core::marker::StructuralPartialEq for mdq::output::MdWriter
impl core::marker::Freeze for mdq::output::MdWriter
impl core::marker::Send for mdq::output::MdWriter
impl core::marker::Sync for mdq::output::MdWriter
impl core::marker::Unpin for mdq::output::MdWriter
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::MdWriter
impl core::panic::unwind_safe::UnwindSafe for mdq::output::MdWriter
impl<T, U> core::convert::Into<U> for mdq::output::MdWriter where U: core::convert::From<T>
pub fn mdq::output::MdWriter::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::MdWriter where U: core::convert::Into<T>
pub type mdq::output::MdWriter::Error = core::convert::Infallible
pub fn mdq::output::MdWriter::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::MdWriter where U: core::convert::TryFrom<T>
pub type mdq::output::MdWriter::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::MdWriter::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::MdWriter where T: core::clone::Clone
pub type mdq::output::MdWriter::Owned = T
pub fn mdq::output::MdWriter::clone_into(&self, target: &mut T)
pub fn mdq::output::MdWriter::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::MdWriter where T: 'static + ?core::marker::Sized
pub fn mdq::output::MdWriter::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::MdWriter where T: ?core::marker::Sized
pub fn mdq::output::MdWriter::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::MdWriter where T: ?core::marker::Sized
pub fn mdq::output::MdWriter::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::MdWriter where T: core::clone::Clone
pub unsafe fn mdq::output::MdWriter::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::MdWriter
pub fn mdq::output::MdWriter::from(t: T) -> T
impl<T> pest::RuleType for mdq::output::MdWriter where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub struct mdq::output::MdWriterOptions
pub mdq::output::MdWriterOptions::footnote_reference_placement: mdq::output::ReferencePlacement
pub mdq::output::MdWriterOptions::include_thematic_breaks: bool
pub mdq::output::MdWriterOptions::inline_options: mdq::output::InlineElemOptions
pub mdq::output::MdWriterOptions::link_reference_placement: mdq::output::ReferencePlacement
pub mdq::output::MdWriterOptions::text_width: core::option::Option<usize>
impl core::clone::Clone for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::clone(&self) -> mdq::output::MdWriterOptions
impl core::cmp::Eq for mdq::output::MdWriterOptions
impl core::cmp::Ord for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::cmp(&self, other: &mdq::output::MdWriterOptions) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::eq(&self, other: &mdq::output::MdWriterOptions) -> bool
impl core::cmp::PartialOrd for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::partial_cmp(&self, other: &mdq::output::MdWriterOptions) -> core::option::Option<core::cmp::Ordering>
impl core::convert::From<&mdq::run::RunOptions> for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::from(cli: &mdq::run::RunOptions) -> Self
impl core::default::Default for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::default() -> mdq::output::MdWriterOptions
impl core::fmt::Debug for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::output::MdWriterOptions
impl core::marker::StructuralPartialEq for mdq::output::MdWriterOptions
impl core::marker::Freeze for mdq::output::MdWriterOptions
impl core::marker::Send for mdq::output::MdWriterOptions
impl core::marker::Sync for mdq::output::MdWriterOptions
impl core::marker::Unpin for mdq::output::MdWriterOptions
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::MdWriterOptions
impl core::panic::unwind_safe::UnwindSafe for mdq::output::MdWriterOptions
impl<T, U> core::convert::Into<U> for mdq::output::MdWriterOptions where U: core::convert::From<T>
pub fn mdq::output::MdWriterOptions::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::MdWriterOptions where U: core::convert::Into<T>
pub type mdq::output::MdWriterOptions::Error = core::convert::Infallible
pub fn mdq::output::MdWriterOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::MdWriterOptions where U: core::convert::TryFrom<T>
pub type mdq::output::MdWriterOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::MdWriterOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::MdWriterOptions where T: core::clone::Clone
pub type mdq::output::MdWriterOptions::Owned = T
pub fn mdq::output::MdWriterOptions::clone_into(&self, target: &mut T)
pub fn mdq::output::MdWriterOptions::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::MdWriterOptions where T: 'static + ?core::marker::Sized
pub fn mdq::output::MdWriterOptions::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::MdWriterOptions where T: ?core::marker::Sized
pub fn mdq::output::MdWriterOptions::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::MdWriterOptions where T: ?core::marker::Sized
pub fn mdq::output::MdWriterOptions::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::MdWriterOptions where T: core::clone::Clone
pub unsafe fn mdq::output::MdWriterOptions::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::from(t: T) -> T
impl<T> pest::RuleType for mdq::output::MdWriterOptions where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub struct mdq::output::MdWriterOptionsBuilder
impl mdq::output::MdWriterOptionsBuilder
pub fn mdq::output::MdWriterOptionsBuilder::build(&self) -> core::result::Result<mdq::output::MdWriterOptions, mdq::output::MdWriterOptionsBuilderError>
pub fn mdq::output::MdWriterOptionsBuilder::footnote_reference_placement(&mut self, value: mdq::output::ReferencePlacement) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::include_thematic_breaks(&mut self, value: bool) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::inline_options(&mut self, value: mdq::output::InlineElemOptions) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::link_reference_placement(&mut self, value: mdq::output::ReferencePlacement) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::text_width(&mut self, value: core::option::Option<usize>) -> &mut Self
impl core::clone::Clone for mdq::output::MdWriterOptionsBuilder
pub fn mdq::output::MdWriterOptionsBuilder::clone(&self) -> mdq::output::MdWriterOptionsBuilder
impl core::default::Default for mdq::output::MdWriterOptionsBuilder
pub fn mdq::output::MdWriterOptionsBuilder::default() -> Self
impl core::marker::Freeze for mdq::output::MdWriterOptionsBuilder
impl core::marker::Send for mdq::output::MdWriterOptionsBuilder
impl core::marker::Sync for mdq::output::MdWriterOptionsBuilder
impl core::marker::Unpin for mdq::output::MdWriterOptionsBuilder
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::MdWriterOptionsBuilder
impl core::panic::unwind_safe::UnwindSafe for mdq::output::MdWriterOptionsBuilder
impl<T, U> core::convert::Into<U> for mdq::output::MdWriterOptionsBuilder where U: core::convert::From<T>
pub fn mdq::output::MdWriterOptionsBuilder::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::MdWriterOptionsBuilder where U: core::convert::Into<T>
pub type mdq::output::MdWriterOptionsBuilder::Error = core::convert::Infallible
pub fn mdq::output::MdWriterOptionsBuilder::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::MdWriterOptionsBuilder where U: core::convert::TryFrom<T>
pub type mdq::output::MdWriterOptionsBuilder::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::MdWriterOptionsBuilder::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::MdWriterOptionsBuilder where T: core::clone::Clone
pub type mdq::output::MdWriterOptionsBuilder::Owned = T
pub fn mdq::output::MdWriterOptionsBuilder::clone_into(&self, target: &mut T)
pub fn mdq::output::MdWriterOptionsBuilder::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::MdWriterOptionsBuilder where T: 'static + ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilder::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::MdWriterOptionsBuilder where T: ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilder::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::MdWriterOptionsBuilder where T: ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilder::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::MdWriterOptionsBuilder where T: core::clone::Clone
pub unsafe fn mdq::output::MdWriterOptionsBuilder::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::MdWriterOptionsBuilder
pub fn mdq::output::MdWriterOptionsBuilder::from(t: T) -> T
pub struct mdq::output::PlainWriter
impl mdq::output::PlainWriter
pub fn mdq::output::PlainWriter::with_options(options: mdq::output::PlainWriterOptions) -> Self
pub fn mdq::output::PlainWriter::write<'md, I, W>(&self, nodes: I, out: &mut W) where I: core::iter::traits::collect::IntoIterator<Item = &'md mdq::md_elem::MdElem>, W: std::io::Write
impl core::clone::Clone for mdq::output::PlainWriter
pub fn mdq::output::PlainWriter::clone(&self) -> mdq::output::PlainWriter
impl core::cmp::Eq for mdq::output::PlainWriter
impl core::cmp::Ord for mdq::output::PlainWriter
pub fn mdq::output::PlainWriter::cmp(&self, other: &mdq::output::PlainWriter) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::output::PlainWriter
pub fn mdq::output::PlainWriter::eq(&self, other: &mdq::output::PlainWriter) -> bool
impl core::cmp::PartialOrd for mdq::output::PlainWriter
pub fn mdq::output::PlainWriter::partial_cmp(&self, other: &mdq::output::PlainWriter) -> core::option::Option<core::cmp::Ordering>
impl core::default::Default for mdq::output::PlainWriter
pub fn mdq::output::PlainWriter::default() -> mdq::output::PlainWriter
impl core::fmt::Debug for mdq::output::PlainWriter
pub fn mdq::output::PlainWriter::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::output::PlainWriter
pub fn mdq::output::PlainWriter::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::output::PlainWriter
impl core::marker::StructuralPartialEq for mdq::output::PlainWriter
impl core::marker::Freeze for mdq::output::PlainWriter
impl core::marker::Send for mdq::output::PlainWriter
impl core::marker::Sync for mdq::output::PlainWriter
impl core::marker::Unpin for mdq::output::PlainWriter
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::PlainWriter
impl core::panic::unwind_safe::UnwindSafe for mdq::output::PlainWriter
impl<T, U> core::convert::Into<U> for mdq::output::PlainWriter where U: core::convert::From<T>
pub fn mdq::output::PlainWriter::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::PlainWriter where U: core::convert::Into<T>
pub type mdq::output::PlainWriter::Error = core::convert::Infallible
pub fn mdq::output::PlainWriter::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::PlainWriter where U: core::convert::TryFrom<T>
pub type mdq::output::PlainWriter::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::PlainWriter::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::PlainWriter where T: core::clone::Clone
pub type mdq::output::PlainWriter::Owned = T
pub fn mdq::output::PlainWriter::clone_into(&self, target: &mut T)
pub fn mdq::output::PlainWriter::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::PlainWriter where T: 'static + ?core::marker::Sized
pub fn mdq::output::PlainWriter::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::PlainWriter where T: ?core::marker::Sized
pub fn mdq::output::PlainWriter::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::PlainWriter where T: ?core::marker::Sized
pub fn mdq::output::PlainWriter::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::PlainWriter where T: core::clone::Clone
pub unsafe fn mdq::output::PlainWriter::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::PlainWriter
pub fn mdq::output::PlainWriter::from(t: T) -> T
impl<T> pest::RuleType for mdq::output::PlainWriter where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub struct mdq::output::PlainWriterOptions
pub mdq::output::PlainWriterOptions::include_breaks: bool
impl core::clone::Clone for mdq::output::PlainWriterOptions
pub fn mdq::output::PlainWriterOptions::clone(&self) -> mdq::output::PlainWriterOptions
impl core::cmp::Eq for mdq::output::PlainWriterOptions
impl core::cmp::Ord for mdq::output::PlainWriterOptions
pub fn mdq::output::PlainWriterOptions::cmp(&self, other: &mdq::output::PlainWriterOptions) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::output::PlainWriterOptions
pub fn mdq::output::PlainWriterOptions::eq(&self, other: &mdq::output::PlainWriterOptions) -> bool
impl core::cmp::PartialOrd for mdq::output::PlainWriterOptions
pub fn mdq::output::PlainWriterOptions::partial_cmp(&self, other: &mdq::output::PlainWriterOptions) -> core::option::Option<core::cmp::Ordering>
impl core::default::Default for mdq::output::PlainWriterOptions
pub fn mdq::output::PlainWriterOptions::default() -> mdq::output::PlainWriterOptions
impl core::fmt::Debug for mdq::output::PlainWriterOptions
pub fn mdq::output::PlainWriterOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::output::PlainWriterOptions
pub fn mdq::output::PlainWriterOptions::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::output::PlainWriterOptions
impl core::marker::StructuralPartialEq for mdq::output::PlainWriterOptions
impl core::marker::Freeze for mdq::output::PlainWriterOptions
impl core::marker::Send for mdq::output::PlainWriterOptions
impl core::marker::Sync for mdq::output::PlainWriterOptions
impl core::marker::Unpin for mdq::output::PlainWriterOptions
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::PlainWriterOptions
impl core::panic::unwind_safe::UnwindSafe for mdq::output::PlainWriterOptions
impl<T, U> core::convert::Into<U> for mdq::output::PlainWriterOptions where U: core::convert::From<T>
pub fn mdq::output::PlainWriterOptions::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::PlainWriterOptions where U: core::convert::Into<T>
pub type mdq::output::PlainWriterOptions::Error = core::convert::Infallible
pub fn mdq::output::PlainWriterOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::PlainWriterOptions where U: core::convert::TryFrom<T>
pub type mdq::output::PlainWriterOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::PlainWriterOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::PlainWriterOptions where T: core::clone::Clone
pub type mdq::output::PlainWriterOptions::Owned = T
pub fn mdq::output::PlainWriterOptions::clone_into(&self, target: &mut T)
pub fn mdq::output::PlainWriterOptions::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::PlainWriterOptions where T: 'static + ?core::marker::Sized
pub fn mdq::output::PlainWriterOptions::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::PlainWriterOptions where T: ?core::marker::Sized
pub fn mdq::output::PlainWriterOptions::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::PlainWriterOptions where T: ?core::marker::Sized
pub fn mdq::output::PlainWriterOptions::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::PlainWriterOptions where T: core::clone::Clone
pub unsafe fn mdq::output::PlainWriterOptions::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::PlainWriterOptions
pub fn mdq::output::PlainWriterOptions::from(t: T) -> T
impl<T> pest::RuleType for mdq::output::PlainWriterOptions where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub struct mdq::output::SerializableMd<'md>
impl<'md> mdq::output::SerializableMd<'md>
pub fn mdq::output::SerializableMd<'md>::new(elems: &'md [mdq::md_elem::MdElem], ctx: &'md mdq::md_elem::MdContext, opts: mdq::output::InlineElemOptions) -> Self
impl<'md> core::clone::Clone for mdq::output::SerializableMd<'md>
pub fn mdq::output::SerializableMd<'md>::clone(&self) -> mdq::output::SerializableMd<'md>
impl<'md> core::default::Default for mdq::output::SerializableMd<'md>
pub fn mdq::output::SerializableMd<'md>::default() -> mdq::output::SerializableMd<'md>
impl<'md> core::fmt::Debug for mdq::output::SerializableMd<'md>
pub fn mdq::output::SerializableMd<'md>::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl<'md> serde::ser::Serialize for mdq::output::SerializableMd<'md>
pub fn mdq::output::SerializableMd<'md>::serialize<__S>(&self, __serializer: __S) -> core::result::Result<<__S as serde::ser::Serializer>::Ok, <__S as serde::ser::Serializer>::Error> where __S: serde::ser::Serializer
impl<'md> core::marker::Freeze for mdq::output::SerializableMd<'md>
impl<'md> core::marker::Send for mdq::output::SerializableMd<'md>
impl<'md> core::marker::Sync for mdq::output::SerializableMd<'md>
impl<'md> core::marker::Unpin for mdq::output::SerializableMd<'md>
impl<'md> core::panic::unwind_safe::RefUnwindSafe for mdq::output::SerializableMd<'md>
impl<'md> core::panic::unwind_safe::UnwindSafe for mdq::output::SerializableMd<'md>
impl<T, U> core::convert::Into<U> for mdq::output::SerializableMd<'md> where U: core::convert::From<T>
pub fn mdq::output::SerializableMd<'md>::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::SerializableMd<'md> where U: core::convert::Into<T>
pub type mdq::output::SerializableMd<'md>::Error = core::convert::Infallible
pub fn mdq::output::SerializableMd<'md>::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::SerializableMd<'md> where U: core::convert::TryFrom<T>
pub type mdq::output::SerializableMd<'md>::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::SerializableMd<'md>::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::SerializableMd<'md> where T: core::clone::Clone
pub type mdq::output::SerializableMd<'md>::Owned = T
pub fn mdq::output::SerializableMd<'md>::clone_into(&self, target: &mut T)
pub fn mdq::output::SerializableMd<'md>::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::SerializableMd<'md> where T: 'static + ?core::marker::Sized
pub fn mdq::output::SerializableMd<'md>::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::SerializableMd<'md> where T: ?core::marker::Sized
pub fn mdq::output::SerializableMd<'md>::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::SerializableMd<'md> where T: ?core::marker::Sized
pub fn mdq::output::SerializableMd<'md>::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::SerializableMd<'md> where T: core::clone::Clone
pub unsafe fn mdq::output::SerializableMd<'md>::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::SerializableMd<'md>
pub fn mdq::output::SerializableMd<'md>::from(t: T) -> T
pub mod mdq::run
pub enum mdq::run::Error
pub mdq::run::Error::FileReadError(mdq::run::Input, std::io::error::Error)
pub mdq::run::Error::MarkdownParse(mdq::md_elem::InvalidMd)
pub mdq::run::Error::QueryParse(mdq::run::QueryParseError)
pub mdq::run::Error::SelectionError(mdq::select::SelectError)
impl core::error::Error for mdq::run::Error
impl core::fmt::Debug for mdq::run::Error
pub fn mdq::run::Error::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::run::Error
pub fn mdq::run::Error::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl !core::marker::Freeze for mdq::run::Error
impl !core::marker::Send for mdq::run::Error
impl !core::marker::Sync for mdq::run::Error
impl core::marker::Unpin for mdq::run::Error
impl !core::panic::unwind_safe::RefUnwindSafe for mdq::run::Error
impl !core::panic::unwind_safe::UnwindSafe for mdq::run::Error
impl<T, U> core::convert::Into<U> for mdq::run::Error where U: core::convert::From<T>
pub fn mdq::run::Error::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::run::Error where U: core::convert::Into<T>
pub type mdq::run::Error::Error = core::convert::Infallible
pub fn mdq::run::Error::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::run::Error where U: core::convert::TryFrom<T>
pub type mdq::run::Error::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::Error::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::string::ToString for mdq::run::Error where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::run::Error::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::run::Error where T: 'static + ?core::marker::Sized
pub fn mdq::run::Error::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::run::Error where T: ?core::marker::Sized
pub fn mdq::run::Error::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::run::Error where T: ?core::marker::Sized
pub fn mdq::run::Error::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::run::Error
pub fn mdq::run::Error::from(t: T) -> T
pub enum mdq::run::Input
pub mdq::run::Input::FilePath(alloc::string::String)
pub mdq::run::Input::Stdin
impl core::clone::Clone for mdq::run::Input
pub fn mdq::run::Input::clone(&self) -> mdq::run::Input
impl core::cmp::Eq for mdq::run::Input
impl core::cmp::Ord for mdq::run::Input
pub fn mdq::run::Input::cmp(&self, other: &mdq::run::Input) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::run::Input
pub fn mdq::run::Input::eq(&self, other: &mdq::run::Input) -> bool
impl core::cmp::PartialOrd for mdq::run::Input
pub fn mdq::run::Input::partial_cmp(&self, other: &mdq::run::Input) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::run::Input
pub fn mdq::run::Input::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::run::Input
pub fn mdq::run::Input::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::run::Input
pub fn mdq::run::Input::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::run::Input
impl core::marker::Freeze for mdq::run::Input
impl core::marker::Send for mdq::run::Input
impl core::marker::Sync for mdq::run::Input
impl core::marker::Unpin for mdq::run::Input
impl core::panic::unwind_safe::RefUnwindSafe for mdq::run::Input
impl core::panic::unwind_safe::UnwindSafe for mdq::run::Input
impl<T, U> core::convert::Into<U> for mdq::run::Input where U: core::convert::From<T>
pub fn mdq::run::Input::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::run::Input where U: core::convert::Into<T>
pub type mdq::run::Input::Error = core::convert::Infallible
pub fn mdq::run::Input::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::run::Input where U: core::convert::TryFrom<T>
pub type mdq::run::Input::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::Input::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::run::Input where T: core::clone::Clone
pub type mdq::run::Input::Owned = T
pub fn mdq::run::Input::clone_into(&self, target: &mut T)
pub fn mdq::run::Input::to_owned(&self) -> T
impl<T> alloc::string::ToString for mdq::run::Input where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::run::Input::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::run::Input where T: 'static + ?core::marker::Sized
pub fn mdq::run::Input::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::run::Input where T: ?core::marker::Sized
pub fn mdq::run::Input::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::run::Input where T: ?core::marker::Sized
pub fn mdq::run::Input::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::run::Input where T: core::clone::Clone
pub unsafe fn mdq::run::Input::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::run::Input
pub fn mdq::run::Input::from(t: T) -> T
pub enum mdq::run::OutputFormat
pub mdq::run::OutputFormat::Json
pub mdq::run::OutputFormat::Markdown
pub mdq::run::OutputFormat::Md
pub mdq::run::OutputFormat::Plain
impl clap_builder::derive::ValueEnum for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::to_possible_value<'a>(&self) -> core::option::Option<clap_builder::builder::possible_value::PossibleValue>
pub fn mdq::run::OutputFormat::value_variants<'a>() -> &'a [Self]
impl core::clone::Clone for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::clone(&self) -> mdq::run::OutputFormat
impl core::cmp::Eq for mdq::run::OutputFormat
impl core::cmp::Ord for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::cmp(&self, other: &mdq::run::OutputFormat) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::eq(&self, other: &mdq::run::OutputFormat) -> bool
impl core::cmp::PartialOrd for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::partial_cmp(&self, other: &mdq::run::OutputFormat) -> core::option::Option<core::cmp::Ordering>
impl core::default::Default for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::default() -> Self
impl core::fmt::Debug for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::run::OutputFormat
impl core::marker::StructuralPartialEq for mdq::run::OutputFormat
impl core::marker::Freeze for mdq::run::OutputFormat
impl core::marker::Send for mdq::run::OutputFormat
impl core::marker::Sync for mdq::run::OutputFormat
impl core::marker::Unpin for mdq::run::OutputFormat
impl core::panic::unwind_safe::RefUnwindSafe for mdq::run::OutputFormat
impl core::panic::unwind_safe::UnwindSafe for mdq::run::OutputFormat
impl<T, U> core::convert::Into<U> for mdq::run::OutputFormat where U: core::convert::From<T>
pub fn mdq::run::OutputFormat::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::run::OutputFormat where U: core::convert::Into<T>
pub type mdq::run::OutputFormat::Error = core::convert::Infallible
pub fn mdq::run::OutputFormat::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::run::OutputFormat where U: core::convert::TryFrom<T>
pub type mdq::run::OutputFormat::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::OutputFormat::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::run::OutputFormat where T: core::clone::Clone
pub type mdq::run::OutputFormat::Owned = T
pub fn mdq::run::OutputFormat::clone_into(&self, target: &mut T)
pub fn mdq::run::OutputFormat::to_owned(&self) -> T
impl<T> alloc::string::ToString for mdq::run::OutputFormat where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::run::OutputFormat::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::run::OutputFormat where T: 'static + ?core::marker::Sized
pub fn mdq::run::OutputFormat::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::run::OutputFormat where T: ?core::marker::Sized
pub fn mdq::run::OutputFormat::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::run::OutputFormat where T: ?core::marker::Sized
pub fn mdq::run::OutputFormat::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::run::OutputFormat where T: core::clone::Clone
pub unsafe fn mdq::run::OutputFormat::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::from(t: T) -> T
impl<T> pest::RuleType for mdq::run::OutputFormat where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
#[non_exhaustive] pub enum mdq::run::RunOptionsBuilderError
pub mdq::run::RunOptionsBuilderError::UninitializedField(&'static str)
pub mdq::run::RunOptionsBuilderError::ValidationError(alloc::string::String)
impl core::convert::From<alloc::string::String> for mdq::run::RunOptionsBuilderError
pub fn mdq::run::RunOptionsBuilderError::from(s: alloc::string::String) -> Self
impl core::convert::From<derive_builder::error::UninitializedFieldError> for mdq::run::RunOptionsBuilderError
pub fn mdq::run::RunOptionsBuilderError::from(s: derive_builder::error::UninitializedFieldError) -> Self
impl core::error::Error for mdq::run::RunOptionsBuilderError
impl core::fmt::Debug for mdq::run::RunOptionsBuilderError
pub fn mdq::run::RunOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::run::RunOptionsBuilderError
pub fn mdq::run::RunOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::Freeze for mdq::run::RunOptionsBuilderError
impl core::marker::Send for mdq::run::RunOptionsBuilderError
impl core::marker::Sync for mdq::run::RunOptionsBuilderError
impl core::marker::Unpin for mdq::run::RunOptionsBuilderError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::run::RunOptionsBuilderError
impl core::panic::unwind_safe::UnwindSafe for mdq::run::RunOptionsBuilderError
impl<T, U> core::convert::Into<U> for mdq::run::RunOptionsBuilderError where U: core::convert::From<T>
pub fn mdq::run::RunOptionsBuilderError::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::run::RunOptionsBuilderError where U: core::convert::Into<T>
pub type mdq::run::RunOptionsBuilderError::Error = core::convert::Infallible
pub fn mdq::run::RunOptionsBuilderError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::run::RunOptionsBuilderError where U: core::convert::TryFrom<T>
pub type mdq::run::RunOptionsBuilderError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::RunOptionsBuilderError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::string::ToString for mdq::run::RunOptionsBuilderError where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilderError::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::run::RunOptionsBuilderError where T: 'static + ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilderError::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::run::RunOptionsBuilderError where T: ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilderError::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::run::RunOptionsBuilderError where T: ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilderError::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::run::RunOptionsBuilderError
pub fn mdq::run::RunOptionsBuilderError::from(t: T) -> T
pub struct mdq::run::QueryParseError
impl core::clone::Clone for mdq::run::QueryParseError
pub fn mdq::run::QueryParseError::clone(&self) -> mdq::run::QueryParseError
impl core::cmp::Eq for mdq::run::QueryParseError
impl core::cmp::PartialEq for mdq::run::QueryParseError
pub fn mdq::run::QueryParseError::eq(&self, other: &mdq::run::QueryParseError) -> bool
impl core::error::Error for mdq::run::QueryParseError
impl core::fmt::Debug for mdq::run::QueryParseError
pub fn mdq::run::QueryParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::run::QueryParseError
pub fn mdq::run::QueryParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::run::QueryParseError
pub fn mdq::run::QueryParseError::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::run::QueryParseError
impl core::marker::Freeze for mdq::run::QueryParseError
impl !core::marker::Send for mdq::run::QueryParseError
impl !core::marker::Sync for mdq::run::QueryParseError
impl core::marker::Unpin for mdq::run::QueryParseError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::run::QueryParseError
impl core::panic::unwind_safe::UnwindSafe for mdq::run::QueryParseError
impl<T, U> core::convert::Into<U> for mdq::run::QueryParseError where U: core::convert::From<T>
pub fn mdq::run::QueryParseError::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::run::QueryParseError where U: core::convert::Into<T>
pub type mdq::run::QueryParseError::Error = core::convert::Infallible
pub fn mdq::run::QueryParseError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::run::QueryParseError where U: core::convert::TryFrom<T>
pub type mdq::run::QueryParseError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::QueryParseError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::run::QueryParseError where T: core::clone::Clone
pub type mdq::run::QueryParseError::Owned = T
pub fn mdq::run::QueryParseError::clone_into(&self, target: &mut T)
pub fn mdq::run::QueryParseError::to_owned(&self) -> T
impl<T> alloc::string::ToString for mdq::run::QueryParseError where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::run::QueryParseError::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::run::QueryParseError where T: 'static + ?core::marker::Sized
pub fn mdq::run::QueryParseError::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::run::QueryParseError where T: ?core::marker::Sized
pub fn mdq::run::QueryParseError::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::run::QueryParseError where T: ?core::marker::Sized
pub fn mdq::run::QueryParseError::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::run::QueryParseError where T: core::clone::Clone
pub unsafe fn mdq::run::QueryParseError::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::run::QueryParseError
pub fn mdq::run::QueryParseError::from(t: T) -> T
pub struct mdq::run::RunOptions
pub mdq::run::RunOptions::add_breaks: core::option::Option<bool>
pub mdq::run::RunOptions::allow_unknown_markdown: bool
pub mdq::run::RunOptions::footnote_pos: core::option::Option<mdq::output::ReferencePlacement>
pub mdq::run::RunOptions::link_format: mdq::output::LinkTransform
pub mdq::run::RunOptions::link_pos: mdq::output::ReferencePlacement
pub mdq::run::RunOptions::markdown_file_paths: alloc::vec::Vec<alloc::string::String>
pub mdq::run::RunOptions::output: mdq::run::OutputFormat
pub mdq::run::RunOptions::quiet: bool
pub mdq::run::RunOptions::renumber_footnotes: bool
pub mdq::run::RunOptions::selectors: alloc::string::String
pub mdq::run::RunOptions::wrap_width: core::option::Option<usize>
impl mdq::run::RunOptions
pub fn mdq::run::RunOptions::should_add_breaks(&self) -> bool
impl core::clone::Clone for mdq::run::RunOptions
pub fn mdq::run::RunOptions::clone(&self) -> mdq::run::RunOptions
impl core::cmp::Eq for mdq::run::RunOptions
impl core::cmp::Ord for mdq::run::RunOptions
pub fn mdq::run::RunOptions::cmp(&self, other: &mdq::run::RunOptions) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::run::RunOptions
pub fn mdq::run::RunOptions::eq(&self, other: &mdq::run::RunOptions) -> bool
impl core::cmp::PartialOrd for mdq::run::RunOptions
pub fn mdq::run::RunOptions::partial_cmp(&self, other: &mdq::run::RunOptions) -> core::option::Option<core::cmp::Ordering>
impl core::convert::From<&mdq::run::RunOptions> for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::from(cli: &mdq::run::RunOptions) -> Self
impl core::default::Default for mdq::run::RunOptions
pub fn mdq::run::RunOptions::default() -> Self
impl core::fmt::Debug for mdq::run::RunOptions
pub fn mdq::run::RunOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::run::RunOptions
pub fn mdq::run::RunOptions::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::run::RunOptions
impl core::marker::Freeze for mdq::run::RunOptions
impl core::marker::Send for mdq::run::RunOptions
impl core::marker::Sync for mdq::run::RunOptions
impl core::marker::Unpin for mdq::run::RunOptions
impl core::panic::unwind_safe::RefUnwindSafe for mdq::run::RunOptions
impl core::panic::unwind_safe::UnwindSafe for mdq::run::RunOptions
impl<T, U> core::convert::Into<U> for mdq::run::RunOptions where U: core::convert::From<T>
pub fn mdq::run::RunOptions::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::run::RunOptions where U: core::convert::Into<T>
pub type mdq::run::RunOptions::Error = core::convert::Infallible
pub fn mdq::run::RunOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::run::RunOptions where U: core::convert::TryFrom<T>
pub type mdq::run::RunOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::RunOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::run::RunOptions where T: core::clone::Clone
pub type mdq::run::RunOptions::Owned = T
pub fn mdq::run::RunOptions::clone_into(&self, target: &mut T)
pub fn mdq::run::RunOptions::to_owned(&self) -> T
impl<T> core::any::Any for mdq::run::RunOptions where T: 'static + ?core::marker::Sized
pub fn mdq::run::RunOptions::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::run::RunOptions where T: ?core::marker::Sized
pub fn mdq::run::RunOptions::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::run::RunOptions where T: ?core::marker::Sized
pub fn mdq::run::RunOptions::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::run::RunOptions where T: core::clone::Clone
pub unsafe fn mdq::run::RunOptions::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::run::RunOptions
pub fn mdq::run::RunOptions::from(t: T) -> T
pub struct mdq::run::RunOptionsBuilder
impl mdq::run::RunOptionsBuilder
pub fn mdq::run::RunOptionsBuilder::add_breaks(&mut self, value: core::option::Option<bool>) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::allow_unknown_markdown(&mut self, value: bool) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::build(&self) -> core::result::Result<mdq::run::RunOptions, mdq::run::RunOptionsBuilderError>
pub fn mdq::run::RunOptionsBuilder::footnote_pos(&mut self, value: core::option::Option<mdq::output::ReferencePlacement>) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::link_format(&mut self, value: mdq::output::LinkTransform) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::link_pos(&mut self, value: mdq::output::ReferencePlacement) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::markdown_file_paths(&mut self, value: alloc::vec::Vec<alloc::string::String>) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::output(&mut self, value: mdq::run::OutputFormat) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::quiet(&mut self, value: bool) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::renumber_footnotes(&mut self, value: bool) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::selectors(&mut self, value: alloc::string::String) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::wrap_width(&mut self, value: core::option::Option<usize>) -> &mut Self
impl core::clone::Clone for mdq::run::RunOptionsBuilder
pub fn mdq::run::RunOptionsBuilder::clone(&self) -> mdq::run::RunOptionsBuilder
impl core::default::Default for mdq::run::RunOptionsBuilder
pub fn mdq::run::RunOptionsBuilder::default() -> Self
impl core::marker::Freeze for mdq::run::RunOptionsBuilder
impl core::marker::Send for mdq::run::RunOptionsBuilder
impl core::marker::Sync for mdq::run::RunOptionsBuilder
impl core::marker::Unpin for mdq::run::RunOptionsBuilder
impl core::panic::unwind_safe::RefUnwindSafe for mdq::run::RunOptionsBuilder
impl core::panic::unwind_safe::UnwindSafe for mdq::run::RunOptionsBuilder
impl<T, U> core::convert::Into<U> for mdq::run::RunOptionsBuilder where U: core::convert::From<T>
pub fn mdq::run::RunOptionsBuilder::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::run::RunOptionsBuilder where U: core::convert::Into<T>
pub type mdq::run::RunOptionsBuilder::Error = core::convert::Infallible
pub fn mdq::run::RunOptionsBuilder::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::run::RunOptionsBuilder where U: core::convert::TryFrom<T>
pub type mdq::run::RunOptionsBuilder::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::RunOptionsBuilder::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::run::RunOptionsBuilder where T: core::clone::Clone
pub type mdq::run::RunOptionsBuilder::Owned = T
pub fn mdq::run::RunOptionsBuilder::clone_into(&self, target: &mut T)
pub fn mdq::run::RunOptionsBuilder::to_owned(&self) -> T
impl<T> core::any::Any for mdq::run::RunOptionsBuilder where T: 'static + ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilder::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::run::RunOptionsBuilder where T: ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilder::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::run::RunOptionsBuilder where T: ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilder::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::run::RunOptionsBuilder where T: core::clone::Clone
pub unsafe fn mdq::run::RunOptionsBuilder::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::run::RunOptionsBuilder
pub fn mdq::run::RunOptionsBuilder::from(t: T) -> T
pub trait mdq::run::OsFacade
pub fn mdq::run::OsFacade::read_all(&self, markdown_file_paths: &[alloc::string::String]) -> core::result::Result<alloc::string::String, mdq::run::Error>
pub fn mdq::run::OsFacade::read_file(&self, path: &str) -> std::io::error::Result<alloc::string::String>
pub fn mdq::run::OsFacade::read_stdin(&self) -> std::io::error::Result<alloc::string::String>
pub fn mdq::run::OsFacade::stdout(&mut self) -> impl std::io::Write
pub fn mdq::run::OsFacade::write_error(&mut self, err: mdq::run::Error)
pub fn mdq::run::run(cli: &mdq::run::RunOptions, os: &mut impl mdq::run::OsFacade) -> bool
pub mod mdq::select
pub enum mdq::select::ListItemTask
pub mdq::select::ListItemTask::Either
pub mdq::select::ListItemTask::None
pub mdq::select::ListItemTask::Selected
pub mdq::select::ListItemTask::Unselected
impl core::clone::Clone for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::clone(&self) -> mdq::select::ListItemTask
impl core::cmp::Eq for mdq::select::ListItemTask
impl core::cmp::Ord for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::cmp(&self, other: &mdq::select::ListItemTask) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::eq(&self, other: &mdq::select::ListItemTask) -> bool
impl core::cmp::PartialOrd for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::partial_cmp(&self, other: &mdq::select::ListItemTask) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::select::ListItemTask
impl core::marker::StructuralPartialEq for mdq::select::ListItemTask
impl core::marker::Freeze for mdq::select::ListItemTask
impl core::marker::Send for mdq::select::ListItemTask
impl core::marker::Sync for mdq::select::ListItemTask
impl core::marker::Unpin for mdq::select::ListItemTask
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::ListItemTask
impl core::panic::unwind_safe::UnwindSafe for mdq::select::ListItemTask
impl<T, U> core::convert::Into<U> for mdq::select::ListItemTask where U: core::convert::From<T>
pub fn mdq::select::ListItemTask::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::ListItemTask where U: core::convert::Into<T>
pub type mdq::select::ListItemTask::Error = core::convert::Infallible
pub fn mdq::select::ListItemTask::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::ListItemTask where U: core::convert::TryFrom<T>
pub type mdq::select::ListItemTask::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::ListItemTask::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::ListItemTask where T: core::clone::Clone
pub type mdq::select::ListItemTask::Owned = T
pub fn mdq::select::ListItemTask::clone_into(&self, target: &mut T)
pub fn mdq::select::ListItemTask::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::ListItemTask where T: 'static + ?core::marker::Sized
pub fn mdq::select::ListItemTask::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::ListItemTask where T: ?core::marker::Sized
pub fn mdq::select::ListItemTask::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::ListItemTask where T: ?core::marker::Sized
pub fn mdq::select::ListItemTask::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::ListItemTask where T: core::clone::Clone
pub unsafe fn mdq::select::ListItemTask::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::from(t: T) -> T
impl<T> pest::RuleType for mdq::select::ListItemTask where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub enum mdq::select::Matcher
pub mdq::select::Matcher::Any
pub mdq::select::Matcher::Any::explicit: bool
pub mdq::select::Matcher::Regex(mdq::select::Regex)
pub mdq::select::Matcher::Text
pub mdq::select::Matcher::Text::anchor_end: bool
pub mdq::select::Matcher::Text::anchor_start: bool
pub mdq::select::Matcher::Text::case_sensitive: bool
pub mdq::select::Matcher::Text::text: alloc::string::String
impl core::clone::Clone for mdq::select::Matcher
pub fn mdq::select::Matcher::clone(&self) -> mdq::select::Matcher
impl core::cmp::Eq for mdq::select::Matcher
impl core::cmp::Ord for mdq::select::Matcher
pub fn mdq::select::Matcher::cmp(&self, other: &mdq::select::Matcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::Matcher
pub fn mdq::select::Matcher::eq(&self, other: &mdq::select::Matcher) -> bool
impl core::cmp::PartialOrd for mdq::select::Matcher
pub fn mdq::select::Matcher::partial_cmp(&self, other: &mdq::select::Matcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::Matcher
pub fn mdq::select::Matcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::Matcher
pub fn mdq::select::Matcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::Matcher
impl core::marker::Freeze for mdq::select::Matcher
impl core::marker::Send for mdq::select::Matcher
impl core::marker::Sync for mdq::select::Matcher
impl core::marker::Unpin for mdq::select::Matcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::Matcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::Matcher
impl<T, U> core::convert::Into<U> for mdq::select::Matcher where U: core::convert::From<T>
pub fn mdq::select::Matcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::Matcher where U: core::convert::Into<T>
pub type mdq::select::Matcher::Error = core::convert::Infallible
pub fn mdq::select::Matcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::Matcher where U: core::convert::TryFrom<T>
pub type mdq::select::Matcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::Matcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::Matcher where T: core::clone::Clone
pub type mdq::select::Matcher::Owned = T
pub fn mdq::select::Matcher::clone_into(&self, target: &mut T)
pub fn mdq::select::Matcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::Matcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::Matcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::Matcher where T: ?core::marker::Sized
pub fn mdq::select::Matcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::Matcher where T: ?core::marker::Sized
pub fn mdq::select::Matcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::Matcher where T: core::clone::Clone
pub unsafe fn mdq::select::Matcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::Matcher
pub fn mdq::select::Matcher::from(t: T) -> T
#[non_exhaustive] pub enum mdq::select::Selector
pub mdq::select::Selector::BlockQuote(mdq::select::BlockQuoteMatcher)
pub mdq::select::Selector::Chain(alloc::vec::Vec<Self>)
pub mdq::select::Selector::CodeBlock(mdq::select::CodeBlockMatcher)
pub mdq::select::Selector::FrontMatter(mdq::select::FrontMatterMatcher)
pub mdq::select::Selector::Html(mdq::select::HtmlMatcher)
pub mdq::select::Selector::Image(mdq::select::LinklikeMatcher)
pub mdq::select::Selector::Link(mdq::select::LinklikeMatcher)
pub mdq::select::Selector::ListItem(mdq::select::ListItemMatcher)
pub mdq::select::Selector::Paragraph(mdq::select::ParagraphMatcher)
pub mdq::select::Selector::Section(mdq::select::SectionMatcher)
pub mdq::select::Selector::Table(mdq::select::TableMatcher)
impl mdq::select::Selector
pub fn mdq::select::Selector::find_nodes(self, doc: mdq::md_elem::MdDoc) -> mdq::select::Result<(alloc::vec::Vec<mdq::md_elem::MdElem>, mdq::md_elem::MdContext)>
impl core::clone::Clone for mdq::select::Selector
pub fn mdq::select::Selector::clone(&self) -> mdq::select::Selector
impl core::cmp::Eq for mdq::select::Selector
impl core::cmp::Ord for mdq::select::Selector
pub fn mdq::select::Selector::cmp(&self, other: &mdq::select::Selector) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::Selector
pub fn mdq::select::Selector::eq(&self, other: &mdq::select::Selector) -> bool
impl core::cmp::PartialOrd for mdq::select::Selector
pub fn mdq::select::Selector::partial_cmp(&self, other: &mdq::select::Selector) -> core::option::Option<core::cmp::Ordering>
impl core::convert::TryFrom<&alloc::string::String> for mdq::select::Selector
pub type mdq::select::Selector::Error = mdq::select::ParseError
pub fn mdq::select::Selector::try_from(value: &alloc::string::String) -> core::result::Result<Self, Self::Error>
impl core::convert::TryFrom<&str> for mdq::select::Selector
pub type mdq::select::Selector::Error = mdq::select::ParseError
pub fn mdq::select::Selector::try_from(value: &str) -> core::result::Result<Self, Self::Error>
impl core::fmt::Debug for mdq::select::Selector
pub fn mdq::select::Selector::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::Selector
pub fn mdq::select::Selector::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::Selector
impl core::marker::Freeze for mdq::select::Selector
impl core::marker::Send for mdq::select::Selector
impl core::marker::Sync for mdq::select::Selector
impl core::marker::Unpin for mdq::select::Selector
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::Selector
impl core::panic::unwind_safe::UnwindSafe for mdq::select::Selector
impl<T, U> core::convert::Into<U> for mdq::select::Selector where U: core::convert::From<T>
pub fn mdq::select::Selector::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::Selector where U: core::convert::Into<T>
pub type mdq::select::Selector::Error = core::convert::Infallible
pub fn mdq::select::Selector::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::Selector where U: core::convert::TryFrom<T>
pub type mdq::select::Selector::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::Selector::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::Selector where T: core::clone::Clone
pub type mdq::select::Selector::Owned = T
pub fn mdq::select::Selector::clone_into(&self, target: &mut T)
pub fn mdq::select::Selector::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::Selector where T: 'static + ?core::marker::Sized
pub fn mdq::select::Selector::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::Selector where T: ?core::marker::Sized
pub fn mdq::select::Selector::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::Selector where T: ?core::marker::Sized
pub fn mdq::select::Selector::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::Selector where T: core::clone::Clone
pub unsafe fn mdq::select::Selector::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::Selector
pub fn mdq::select::Selector::from(t: T) -> T
pub struct mdq::select::BlockQuoteMatcher
pub mdq::select::BlockQuoteMatcher::text: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::clone(&self) -> mdq::select::BlockQuoteMatcher
impl core::cmp::Eq for mdq::select::BlockQuoteMatcher
impl core::cmp::Ord for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::cmp(&self, other: &mdq::select::BlockQuoteMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::eq(&self, other: &mdq::select::BlockQuoteMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::partial_cmp(&self, other: &mdq::select::BlockQuoteMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::BlockQuoteMatcher
impl core::marker::Freeze for mdq::select::BlockQuoteMatcher
impl core::marker::Send for mdq::select::BlockQuoteMatcher
impl core::marker::Sync for mdq::select::BlockQuoteMatcher
impl core::marker::Unpin for mdq::select::BlockQuoteMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::BlockQuoteMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::BlockQuoteMatcher
impl<T, U> core::convert::Into<U> for mdq::select::BlockQuoteMatcher where U: core::convert::From<T>
pub fn mdq::select::BlockQuoteMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::BlockQuoteMatcher where U: core::convert::Into<T>
pub type mdq::select::BlockQuoteMatcher::Error = core::convert::Infallible
pub fn mdq::select::BlockQuoteMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::BlockQuoteMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::BlockQuoteMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::BlockQuoteMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::BlockQuoteMatcher where T: core::clone::Clone
pub type mdq::select::BlockQuoteMatcher::Owned = T
pub fn mdq::select::BlockQuoteMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::BlockQuoteMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::BlockQuoteMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::BlockQuoteMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::BlockQuoteMatcher where T: ?core::marker::Sized
pub fn mdq::select::BlockQuoteMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::BlockQuoteMatcher where T: ?core::marker::Sized
pub fn mdq::select::BlockQuoteMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::BlockQuoteMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::BlockQuoteMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::from(t: T) -> T
pub struct mdq::select::CodeBlockMatcher
pub mdq::select::CodeBlockMatcher::contents: mdq::select::MatchReplace
pub mdq::select::CodeBlockMatcher::language: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::clone(&self) -> mdq::select::CodeBlockMatcher
impl core::cmp::Eq for mdq::select::CodeBlockMatcher
impl core::cmp::Ord for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::cmp(&self, other: &mdq::select::CodeBlockMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::eq(&self, other: &mdq::select::CodeBlockMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::partial_cmp(&self, other: &mdq::select::CodeBlockMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::CodeBlockMatcher
impl core::marker::Freeze for mdq::select::CodeBlockMatcher
impl core::marker::Send for mdq::select::CodeBlockMatcher
impl core::marker::Sync for mdq::select::CodeBlockMatcher
impl core::marker::Unpin for mdq::select::CodeBlockMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::CodeBlockMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::CodeBlockMatcher
impl<T, U> core::convert::Into<U> for mdq::select::CodeBlockMatcher where U: core::convert::From<T>
pub fn mdq::select::CodeBlockMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::CodeBlockMatcher where U: core::convert::Into<T>
pub type mdq::select::CodeBlockMatcher::Error = core::convert::Infallible
pub fn mdq::select::CodeBlockMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::CodeBlockMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::CodeBlockMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::CodeBlockMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::CodeBlockMatcher where T: core::clone::Clone
pub type mdq::select::CodeBlockMatcher::Owned = T
pub fn mdq::select::CodeBlockMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::CodeBlockMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::CodeBlockMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::CodeBlockMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::CodeBlockMatcher where T: ?core::marker::Sized
pub fn mdq::select::CodeBlockMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::CodeBlockMatcher where T: ?core::marker::Sized
pub fn mdq::select::CodeBlockMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::CodeBlockMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::CodeBlockMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::from(t: T) -> T
pub struct mdq::select::FrontMatterMatcher
pub mdq::select::FrontMatterMatcher::text: mdq::select::MatchReplace
pub mdq::select::FrontMatterMatcher::variant: core::option::Option<mdq::md_elem::elem::FrontMatterVariant>
impl core::clone::Clone for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::clone(&self) -> mdq::select::FrontMatterMatcher
impl core::cmp::Eq for mdq::select::FrontMatterMatcher
impl core::cmp::Ord for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::cmp(&self, other: &mdq::select::FrontMatterMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::eq(&self, other: &mdq::select::FrontMatterMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::partial_cmp(&self, other: &mdq::select::FrontMatterMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::FrontMatterMatcher
impl core::marker::Freeze for mdq::select::FrontMatterMatcher
impl core::marker::Send for mdq::select::FrontMatterMatcher
impl core::marker::Sync for mdq::select::FrontMatterMatcher
impl core::marker::Unpin for mdq::select::FrontMatterMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::FrontMatterMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::FrontMatterMatcher
impl<T, U> core::convert::Into<U> for mdq::select::FrontMatterMatcher where U: core::convert::From<T>
pub fn mdq::select::FrontMatterMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::FrontMatterMatcher where U: core::convert::Into<T>
pub type mdq::select::FrontMatterMatcher::Error = core::convert::Infallible
pub fn mdq::select::FrontMatterMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::FrontMatterMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::FrontMatterMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::FrontMatterMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::FrontMatterMatcher where T: core::clone::Clone
pub type mdq::select::FrontMatterMatcher::Owned = T
pub fn mdq::select::FrontMatterMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::FrontMatterMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::FrontMatterMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::FrontMatterMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::FrontMatterMatcher where T: ?core::marker::Sized
pub fn mdq::select::FrontMatterMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::FrontMatterMatcher where T: ?core::marker::Sized
pub fn mdq::select::FrontMatterMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::FrontMatterMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::FrontMatterMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::from(t: T) -> T
pub struct mdq::select::HtmlMatcher
pub mdq::select::HtmlMatcher::html: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::clone(&self) -> mdq::select::HtmlMatcher
impl core::cmp::Eq for mdq::select::HtmlMatcher
impl core::cmp::Ord for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::cmp(&self, other: &mdq::select::HtmlMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::eq(&self, other: &mdq::select::HtmlMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::partial_cmp(&self, other: &mdq::select::HtmlMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::HtmlMatcher
impl core::marker::Freeze for mdq::select::HtmlMatcher
impl core::marker::Send for mdq::select::HtmlMatcher
impl core::marker::Sync for mdq::select::HtmlMatcher
impl core::marker::Unpin for mdq::select::HtmlMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::HtmlMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::HtmlMatcher
impl<T, U> core::convert::Into<U> for mdq::select::HtmlMatcher where U: core::convert::From<T>
pub fn mdq::select::HtmlMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::HtmlMatcher where U: core::convert::Into<T>
pub type mdq::select::HtmlMatcher::Error = core::convert::Infallible
pub fn mdq::select::HtmlMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::HtmlMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::HtmlMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::HtmlMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::HtmlMatcher where T: core::clone::Clone
pub type mdq::select::HtmlMatcher::Owned = T
pub fn mdq::select::HtmlMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::HtmlMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::HtmlMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::HtmlMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::HtmlMatcher where T: ?core::marker::Sized
pub fn mdq::select::HtmlMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::HtmlMatcher where T: ?core::marker::Sized
pub fn mdq::select::HtmlMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::HtmlMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::HtmlMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::from(t: T) -> T
pub struct mdq::select::LinklikeMatcher
pub mdq::select::LinklikeMatcher::display_matcher: mdq::select::MatchReplace
pub mdq::select::LinklikeMatcher::url_matcher: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::clone(&self) -> mdq::select::LinklikeMatcher
impl core::cmp::Eq for mdq::select::LinklikeMatcher
impl core::cmp::Ord for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::cmp(&self, other: &mdq::select::LinklikeMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::eq(&self, other: &mdq::select::LinklikeMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::partial_cmp(&self, other: &mdq::select::LinklikeMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::LinklikeMatcher
impl core::marker::Freeze for mdq::select::LinklikeMatcher
impl core::marker::Send for mdq::select::LinklikeMatcher
impl core::marker::Sync for mdq::select::LinklikeMatcher
impl core::marker::Unpin for mdq::select::LinklikeMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::LinklikeMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::LinklikeMatcher
impl<T, U> core::convert::Into<U> for mdq::select::LinklikeMatcher where U: core::convert::From<T>
pub fn mdq::select::LinklikeMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::LinklikeMatcher where U: core::convert::Into<T>
pub type mdq::select::LinklikeMatcher::Error = core::convert::Infallible
pub fn mdq::select::LinklikeMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::LinklikeMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::LinklikeMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::LinklikeMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::LinklikeMatcher where T: core::clone::Clone
pub type mdq::select::LinklikeMatcher::Owned = T
pub fn mdq::select::LinklikeMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::LinklikeMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::LinklikeMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::LinklikeMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::LinklikeMatcher where T: ?core::marker::Sized
pub fn mdq::select::LinklikeMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::LinklikeMatcher where T: ?core::marker::Sized
pub fn mdq::select::LinklikeMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::LinklikeMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::LinklikeMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::from(t: T) -> T
pub struct mdq::select::ListItemMatcher
pub mdq::select::ListItemMatcher::matcher: mdq::select::MatchReplace
pub mdq::select::ListItemMatcher::ordered: bool
pub mdq::select::ListItemMatcher::task: mdq::select::ListItemTask
impl core::clone::Clone for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::clone(&self) -> mdq::select::ListItemMatcher
impl core::cmp::Eq for mdq::select::ListItemMatcher
impl core::cmp::Ord for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::cmp(&self, other: &mdq::select::ListItemMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::eq(&self, other: &mdq::select::ListItemMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::partial_cmp(&self, other: &mdq::select::ListItemMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::ListItemMatcher
impl core::marker::Freeze for mdq::select::ListItemMatcher
impl core::marker::Send for mdq::select::ListItemMatcher
impl core::marker::Sync for mdq::select::ListItemMatcher
impl core::marker::Unpin for mdq::select::ListItemMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::ListItemMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::ListItemMatcher
impl<T, U> core::convert::Into<U> for mdq::select::ListItemMatcher where U: core::convert::From<T>
pub fn mdq::select::ListItemMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::ListItemMatcher where U: core::convert::Into<T>
pub type mdq::select::ListItemMatcher::Error = core::convert::Infallible
pub fn mdq::select::ListItemMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::ListItemMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::ListItemMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::ListItemMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::ListItemMatcher where T: core::clone::Clone
pub type mdq::select::ListItemMatcher::Owned = T
pub fn mdq::select::ListItemMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::ListItemMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::ListItemMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::ListItemMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::ListItemMatcher where T: ?core::marker::Sized
pub fn mdq::select::ListItemMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::ListItemMatcher where T: ?core::marker::Sized
pub fn mdq::select::ListItemMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::ListItemMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::ListItemMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::from(t: T) -> T
pub struct mdq::select::MatchReplace
pub mdq::select::MatchReplace::matcher: mdq::select::Matcher
pub mdq::select::MatchReplace::replacement: core::option::Option<alloc::string::String>
impl core::clone::Clone for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::clone(&self) -> mdq::select::MatchReplace
impl core::cmp::Eq for mdq::select::MatchReplace
impl core::cmp::Ord for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::cmp(&self, other: &mdq::select::MatchReplace) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::eq(&self, other: &mdq::select::MatchReplace) -> bool
impl core::cmp::PartialOrd for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::partial_cmp(&self, other: &mdq::select::MatchReplace) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::MatchReplace
impl core::marker::Freeze for mdq::select::MatchReplace
impl core::marker::Send for mdq::select::MatchReplace
impl core::marker::Sync for mdq::select::MatchReplace
impl core::marker::Unpin for mdq::select::MatchReplace
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::MatchReplace
impl core::panic::unwind_safe::UnwindSafe for mdq::select::MatchReplace
impl<T, U> core::convert::Into<U> for mdq::select::MatchReplace where U: core::convert::From<T>
pub fn mdq::select::MatchReplace::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::MatchReplace where U: core::convert::Into<T>
pub type mdq::select::MatchReplace::Error = core::convert::Infallible
pub fn mdq::select::MatchReplace::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::MatchReplace where U: core::convert::TryFrom<T>
pub type mdq::select::MatchReplace::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::MatchReplace::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::MatchReplace where T: core::clone::Clone
pub type mdq::select::MatchReplace::Owned = T
pub fn mdq::select::MatchReplace::clone_into(&self, target: &mut T)
pub fn mdq::select::MatchReplace::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::MatchReplace where T: 'static + ?core::marker::Sized
pub fn mdq::select::MatchReplace::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::MatchReplace where T: ?core::marker::Sized
pub fn mdq::select::MatchReplace::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::MatchReplace where T: ?core::marker::Sized
pub fn mdq::select::MatchReplace::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::MatchReplace where T: core::clone::Clone
pub unsafe fn mdq::select::MatchReplace::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::from(t: T) -> T
pub struct mdq::select::ParagraphMatcher
pub mdq::select::ParagraphMatcher::text: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::clone(&self) -> mdq::select::ParagraphMatcher
impl core::cmp::Eq for mdq::select::ParagraphMatcher
impl core::cmp::Ord for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::cmp(&self, other: &mdq::select::ParagraphMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::eq(&self, other: &mdq::select::ParagraphMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::partial_cmp(&self, other: &mdq::select::ParagraphMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::ParagraphMatcher
impl core::marker::Freeze for mdq::select::ParagraphMatcher
impl core::marker::Send for mdq::select::ParagraphMatcher
impl core::marker::Sync for mdq::select::ParagraphMatcher
impl core::marker::Unpin for mdq::select::ParagraphMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::ParagraphMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::ParagraphMatcher
impl<T, U> core::convert::Into<U> for mdq::select::ParagraphMatcher where U: core::convert::From<T>
pub fn mdq::select::ParagraphMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::ParagraphMatcher where U: core::convert::Into<T>
pub type mdq::select::ParagraphMatcher::Error = core::convert::Infallible
pub fn mdq::select::ParagraphMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::ParagraphMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::ParagraphMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::ParagraphMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::ParagraphMatcher where T: core::clone::Clone
pub type mdq::select::ParagraphMatcher::Owned = T
pub fn mdq::select::ParagraphMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::ParagraphMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::ParagraphMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::ParagraphMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::ParagraphMatcher where T: ?core::marker::Sized
pub fn mdq::select::ParagraphMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::ParagraphMatcher where T: ?core::marker::Sized
pub fn mdq::select::ParagraphMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::ParagraphMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::ParagraphMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::from(t: T) -> T
pub struct mdq::select::ParseError
impl mdq::select::ParseError
pub fn mdq::select::ParseError::to_string(&self, query_text: &str) -> alloc::string::String
impl core::clone::Clone for mdq::select::ParseError
pub fn mdq::select::ParseError::clone(&self) -> mdq::select::ParseError
impl core::cmp::Eq for mdq::select::ParseError
impl core::cmp::PartialEq for mdq::select::ParseError
pub fn mdq::select::ParseError::eq(&self, other: &mdq::select::ParseError) -> bool
impl core::error::Error for mdq::select::ParseError
pub fn mdq::select::ParseError::source(&self) -> core::option::Option<&(dyn core::error::Error + 'static)>
impl core::fmt::Debug for mdq::select::ParseError
pub fn mdq::select::ParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::select::ParseError
pub fn mdq::select::ParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::ParseError
pub fn mdq::select::ParseError::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::ParseError
impl core::marker::Freeze for mdq::select::ParseError
impl !core::marker::Send for mdq::select::ParseError
impl !core::marker::Sync for mdq::select::ParseError
impl core::marker::Unpin for mdq::select::ParseError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::ParseError
impl core::panic::unwind_safe::UnwindSafe for mdq::select::ParseError
impl<T, U> core::convert::Into<U> for mdq::select::ParseError where U: core::convert::From<T>
pub fn mdq::select::ParseError::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::ParseError where U: core::convert::Into<T>
pub type mdq::select::ParseError::Error = core::convert::Infallible
pub fn mdq::select::ParseError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::ParseError where U: core::convert::TryFrom<T>
pub type mdq::select::ParseError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::ParseError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::ParseError where T: core::clone::Clone
pub type mdq::select::ParseError::Owned = T
pub fn mdq::select::ParseError::clone_into(&self, target: &mut T)
pub fn mdq::select::ParseError::to_owned(&self) -> T
impl<T> alloc::string::ToString for mdq::select::ParseError where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::select::ParseError::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::select::ParseError where T: 'static + ?core::marker::Sized
pub fn mdq::select::ParseError::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::ParseError where T: ?core::marker::Sized
pub fn mdq::select::ParseError::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::ParseError where T: ?core::marker::Sized
pub fn mdq::select::ParseError::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::ParseError where T: core::clone::Clone
pub unsafe fn mdq::select::ParseError::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::ParseError
pub fn mdq::select::ParseError::from(t: T) -> T
pub struct mdq::select::Regex
impl core::clone::Clone for mdq::select::Regex
pub fn mdq::select::Regex::clone(&self) -> mdq::select::Regex
impl core::cmp::Eq for mdq::select::Regex
impl core::cmp::Ord for mdq::select::Regex
pub fn mdq::select::Regex::cmp(&self, other: &Self) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::Regex
pub fn mdq::select::Regex::eq(&self, other: &Self) -> bool
impl core::cmp::PartialOrd for mdq::select::Regex
pub fn mdq::select::Regex::partial_cmp(&self, other: &Self) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::Regex
pub fn mdq::select::Regex::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::Regex
pub fn mdq::select::Regex::hash<H: core::hash::Hasher>(&self, state: &mut H)
impl core::marker::Freeze for mdq::select::Regex
impl core::marker::Send for mdq::select::Regex
impl core::marker::Sync for mdq::select::Regex
impl core::marker::Unpin for mdq::select::Regex
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::Regex
impl core::panic::unwind_safe::UnwindSafe for mdq::select::Regex
impl<T, U> core::convert::Into<U> for mdq::select::Regex where U: core::convert::From<T>
pub fn mdq::select::Regex::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::Regex where U: core::convert::Into<T>
pub type mdq::select::Regex::Error = core::convert::Infallible
pub fn mdq::select::Regex::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::Regex where U: core::convert::TryFrom<T>
pub type mdq::select::Regex::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::Regex::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::Regex where T: core::clone::Clone
pub type mdq::select::Regex::Owned = T
pub fn mdq::select::Regex::clone_into(&self, target: &mut T)
pub fn mdq::select::Regex::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::Regex where T: 'static + ?core::marker::Sized
pub fn mdq::select::Regex::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::Regex where T: ?core::marker::Sized
pub fn mdq::select::Regex::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::Regex where T: ?core::marker::Sized
pub fn mdq::select::Regex::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::Regex where T: core::clone::Clone
pub unsafe fn mdq::select::Regex::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::Regex
pub fn mdq::select::Regex::from(t: T) -> T
pub struct mdq::select::SectionMatcher
pub mdq::select::SectionMatcher::title: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::clone(&self) -> mdq::select::SectionMatcher
impl core::cmp::Eq for mdq::select::SectionMatcher
impl core::cmp::Ord for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::cmp(&self, other: &mdq::select::SectionMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::eq(&self, other: &mdq::select::SectionMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::partial_cmp(&self, other: &mdq::select::SectionMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::SectionMatcher
impl core::marker::Freeze for mdq::select::SectionMatcher
impl core::marker::Send for mdq::select::SectionMatcher
impl core::marker::Sync for mdq::select::SectionMatcher
impl core::marker::Unpin for mdq::select::SectionMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::SectionMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::SectionMatcher
impl<T, U> core::convert::Into<U> for mdq::select::SectionMatcher where U: core::convert::From<T>
pub fn mdq::select::SectionMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::SectionMatcher where U: core::convert::Into<T>
pub type mdq::select::SectionMatcher::Error = core::convert::Infallible
pub fn mdq::select::SectionMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::SectionMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::SectionMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::SectionMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::SectionMatcher where T: core::clone::Clone
pub type mdq::select::SectionMatcher::Owned = T
pub fn mdq::select::SectionMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::SectionMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::SectionMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::SectionMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::SectionMatcher where T: ?core::marker::Sized
pub fn mdq::select::SectionMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::SectionMatcher where T: ?core::marker::Sized
pub fn mdq::select::SectionMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::SectionMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::SectionMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::from(t: T) -> T
pub struct mdq::select::SelectError
impl core::clone::Clone for mdq::select::SelectError
pub fn mdq::select::SelectError::clone(&self) -> mdq::select::SelectError
impl core::cmp::Eq for mdq::select::SelectError
impl core::cmp::PartialEq for mdq::select::SelectError
pub fn mdq::select::SelectError::eq(&self, other: &mdq::select::SelectError) -> bool
impl core::error::Error for mdq::select::SelectError
impl core::fmt::Debug for mdq::select::SelectError
pub fn mdq::select::SelectError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::select::SelectError
pub fn mdq::select::SelectError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::SelectError
pub fn mdq::select::SelectError::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::SelectError
impl core::marker::Freeze for mdq::select::SelectError
impl core::marker::Send for mdq::select::SelectError
impl core::marker::Sync for mdq::select::SelectError
impl core::marker::Unpin for mdq::select::SelectError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::SelectError
impl core::panic::unwind_safe::UnwindSafe for mdq::select::SelectError
impl<T, U> core::convert::Into<U> for mdq::select::SelectError where U: core::convert::From<T>
pub fn mdq::select::SelectError::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::SelectError where U: core::convert::Into<T>
pub type mdq::select::SelectError::Error = core::convert::Infallible
pub fn mdq::select::SelectError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::SelectError where U: core::convert::TryFrom<T>
pub type mdq::select::SelectError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::SelectError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::SelectError where T: core::clone::Clone
pub type mdq::select::SelectError::Owned = T
pub fn mdq::select::SelectError::clone_into(&self, target: &mut T)
pub fn mdq::select::SelectError::to_owned(&self) -> T
impl<T> alloc::string::ToString for mdq::select::SelectError where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::select::SelectError::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::select::SelectError where T: 'static + ?core::marker::Sized
pub fn mdq::select::SelectError::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::SelectError where T: ?core::marker::Sized
pub fn mdq::select::SelectError::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::SelectError where T: ?core::marker::Sized
pub fn mdq::select::SelectError::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::SelectError where T: core::clone::Clone
pub unsafe fn mdq::select::SelectError::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::SelectError
pub fn mdq::select::SelectError::from(t: T) -> T
pub struct mdq::select::TableMatcher
pub mdq::select::TableMatcher::headers: mdq::select::MatchReplace
pub mdq::select::TableMatcher::rows: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::clone(&self) -> mdq::select::TableMatcher
impl core::cmp::Eq for mdq::select::TableMatcher
impl core::cmp::Ord for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::cmp(&self, other: &mdq::select::TableMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::eq(&self, other: &mdq::select::TableMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::partial_cmp(&self, other: &mdq::select::TableMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::TableMatcher
impl core::marker::Freeze for mdq::select::TableMatcher
impl core::marker::Send for mdq::select::TableMatcher
impl core::marker::Sync for mdq::select::TableMatcher
impl core::marker::Unpin for mdq::select::TableMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::TableMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::TableMatcher
impl<T, U> core::convert::Into<U> for mdq::select::TableMatcher where U: core::convert::From<T>
pub fn mdq::select::TableMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::TableMatcher where U: core::convert::Into<T>
pub type mdq::select::TableMatcher::Error = core::convert::Infallible
pub fn mdq::select::TableMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::TableMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::TableMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::TableMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::TableMatcher where T: core::clone::Clone
pub type mdq::select::TableMatcher::Owned = T
pub fn mdq::select::TableMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::TableMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::TableMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::TableMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::TableMatcher where T: ?core::marker::Sized
pub fn mdq::select::TableMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::TableMatcher where T: ?core::marker::Sized
pub fn mdq::select::TableMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::TableMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::TableMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::from(t: T) -> T
pub type mdq::select::Result<T> = core::result::Result<T, mdq::select::SelectError>
