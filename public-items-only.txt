
pub mod mdq
pub mod mdq::md_elem
pub mod mdq::md_elem::elem
pub enum mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::clone(&self) -> mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::eq(&self, other: &mdq::md_elem::elem::CodeVariant) -> bool
pub fn mdq::md_elem::elem::CodeVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::CodeVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::CodeVariant::into(self) -> U
pub type mdq::md_elem::elem::CodeVariant::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::CodeVariant::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::CodeVariant::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::CodeVariant::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::CodeVariant::Owned = T
pub fn mdq::md_elem::elem::CodeVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::CodeVariant::to_owned(&self) -> T
pub fn mdq::md_elem::elem::CodeVariant::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::CodeVariant::borrow(&self) -> &T
pub fn mdq::md_elem::elem::CodeVariant::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::CodeVariant::from(t: T) -> T
pub enum mdq::md_elem::elem::ColumnAlignment
pub fn mdq::md_elem::elem::ColumnAlignment::clone(&self) -> mdq::md_elem::elem::ColumnAlignment
pub fn mdq::md_elem::elem::ColumnAlignment::cmp(&self, other: &mdq::md_elem::elem::ColumnAlignment) -> core::cmp::Ordering
pub fn mdq::md_elem::elem::ColumnAlignment::eq(&self, other: &mdq::md_elem::elem::ColumnAlignment) -> bool
pub fn mdq::md_elem::elem::ColumnAlignment::partial_cmp(&self, other: &mdq::md_elem::elem::ColumnAlignment) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::md_elem::elem::ColumnAlignment::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::ColumnAlignment::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::ColumnAlignment::into(self) -> U
pub type mdq::md_elem::elem::ColumnAlignment::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::ColumnAlignment::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::ColumnAlignment::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::ColumnAlignment::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::ColumnAlignment::Owned = T
pub fn mdq::md_elem::elem::ColumnAlignment::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::ColumnAlignment::to_owned(&self) -> T
pub fn mdq::md_elem::elem::ColumnAlignment::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::ColumnAlignment::borrow(&self) -> &T
pub fn mdq::md_elem::elem::ColumnAlignment::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::ColumnAlignment::from(t: T) -> T
pub enum mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::name(self) -> &'static str
pub fn mdq::md_elem::elem::FrontMatterVariant::separator(self) -> &'static str
pub fn mdq::md_elem::elem::FrontMatterVariant::clone(&self) -> mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::cmp(&self, other: &mdq::md_elem::elem::FrontMatterVariant) -> core::cmp::Ordering
pub fn mdq::md_elem::elem::FrontMatterVariant::eq(&self, other: &mdq::md_elem::elem::FrontMatterVariant) -> bool
pub fn mdq::md_elem::elem::FrontMatterVariant::partial_cmp(&self, other: &mdq::md_elem::elem::FrontMatterVariant) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::md_elem::elem::FrontMatterVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::FrontMatterVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::FrontMatterVariant::into(self) -> U
pub type mdq::md_elem::elem::FrontMatterVariant::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::FrontMatterVariant::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::FrontMatterVariant::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::FrontMatterVariant::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::FrontMatterVariant::Owned = T
pub fn mdq::md_elem::elem::FrontMatterVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::FrontMatterVariant::to_owned(&self) -> T
pub fn mdq::md_elem::elem::FrontMatterVariant::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::FrontMatterVariant::borrow(&self) -> &T
pub fn mdq::md_elem::elem::FrontMatterVariant::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::FrontMatterVariant::from(t: T) -> T
pub enum mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::clone(&self) -> mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::eq(&self, other: &mdq::md_elem::elem::Inline) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Inline) -> Self
pub fn mdq::md_elem::elem::Inline::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Inline::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Inline::into(self) -> U
pub type mdq::md_elem::elem::Inline::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Inline::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::Inline::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Inline::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::Inline::Owned = T
pub fn mdq::md_elem::elem::Inline::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Inline::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Inline::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Inline::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Inline::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Inline::from(t: T) -> T
pub enum mdq::md_elem::elem::LinkReference
pub fn mdq::md_elem::elem::LinkReference::clone(&self) -> mdq::md_elem::elem::LinkReference
pub fn mdq::md_elem::elem::LinkReference::cmp(&self, other: &mdq::md_elem::elem::LinkReference) -> core::cmp::Ordering
pub fn mdq::md_elem::elem::LinkReference::eq(&self, other: &mdq::md_elem::elem::LinkReference) -> bool
pub fn mdq::md_elem::elem::LinkReference::partial_cmp(&self, other: &mdq::md_elem::elem::LinkReference) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::md_elem::elem::LinkReference::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::LinkReference::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::LinkReference::into(self) -> U
pub type mdq::md_elem::elem::LinkReference::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::LinkReference::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::LinkReference::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::LinkReference::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::LinkReference::Owned = T
pub fn mdq::md_elem::elem::LinkReference::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::LinkReference::to_owned(&self) -> T
pub fn mdq::md_elem::elem::LinkReference::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::LinkReference::borrow(&self) -> &T
pub fn mdq::md_elem::elem::LinkReference::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::LinkReference::from(t: T) -> T
pub enum mdq::md_elem::elem::SpanVariant
pub fn mdq::md_elem::elem::SpanVariant::clone(&self) -> mdq::md_elem::elem::SpanVariant
pub fn mdq::md_elem::elem::SpanVariant::cmp(&self, other: &mdq::md_elem::elem::SpanVariant) -> core::cmp::Ordering
pub fn mdq::md_elem::elem::SpanVariant::eq(&self, other: &mdq::md_elem::elem::SpanVariant) -> bool
pub fn mdq::md_elem::elem::SpanVariant::partial_cmp(&self, other: &mdq::md_elem::elem::SpanVariant) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::md_elem::elem::SpanVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::SpanVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::SpanVariant::into(self) -> U
pub type mdq::md_elem::elem::SpanVariant::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::SpanVariant::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::SpanVariant::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::SpanVariant::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::SpanVariant::Owned = T
pub fn mdq::md_elem::elem::SpanVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::SpanVariant::to_owned(&self) -> T
pub fn mdq::md_elem::elem::SpanVariant::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::SpanVariant::borrow(&self) -> &T
pub fn mdq::md_elem::elem::SpanVariant::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::SpanVariant::from(t: T) -> T
pub enum mdq::md_elem::elem::TextVariant
pub fn mdq::md_elem::elem::TextVariant::clone(&self) -> mdq::md_elem::elem::TextVariant
pub fn mdq::md_elem::elem::TextVariant::cmp(&self, other: &mdq::md_elem::elem::TextVariant) -> core::cmp::Ordering
pub fn mdq::md_elem::elem::TextVariant::eq(&self, other: &mdq::md_elem::elem::TextVariant) -> bool
pub fn mdq::md_elem::elem::TextVariant::partial_cmp(&self, other: &mdq::md_elem::elem::TextVariant) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::md_elem::elem::TextVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::TextVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::TextVariant::into(self) -> U
pub type mdq::md_elem::elem::TextVariant::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::TextVariant::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::TextVariant::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::TextVariant::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::TextVariant::Owned = T
pub fn mdq::md_elem::elem::TextVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::TextVariant::to_owned(&self) -> T
pub fn mdq::md_elem::elem::TextVariant::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::TextVariant::borrow(&self) -> &T
pub fn mdq::md_elem::elem::TextVariant::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::TextVariant::from(t: T) -> T
pub struct mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::clone(&self) -> mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::eq(&self, other: &mdq::md_elem::elem::BlockHtml) -> bool
pub fn mdq::md_elem::elem::BlockHtml::from(value: alloc::string::String) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockHtml) -> Self
pub fn mdq::md_elem::elem::BlockHtml::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::BlockHtml::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::BlockHtml::into(self) -> U
pub type mdq::md_elem::elem::BlockHtml::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::BlockHtml::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::BlockHtml::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::BlockHtml::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::BlockHtml::Owned = T
pub fn mdq::md_elem::elem::BlockHtml::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::BlockHtml::to_owned(&self) -> T
pub fn mdq::md_elem::elem::BlockHtml::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::BlockHtml::borrow(&self) -> &T
pub fn mdq::md_elem::elem::BlockHtml::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::BlockHtml::from(t: T) -> T
pub struct mdq::md_elem::elem::BlockQuote
pub fn mdq::md_elem::elem::BlockQuote::clone(&self) -> mdq::md_elem::elem::BlockQuote
pub fn mdq::md_elem::elem::BlockQuote::eq(&self, other: &mdq::md_elem::elem::BlockQuote) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockQuote) -> Self
pub fn mdq::md_elem::elem::BlockQuote::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::BlockQuote::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::BlockQuote::into(self) -> U
pub type mdq::md_elem::elem::BlockQuote::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::BlockQuote::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::BlockQuote::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::BlockQuote::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::BlockQuote::Owned = T
pub fn mdq::md_elem::elem::BlockQuote::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::BlockQuote::to_owned(&self) -> T
pub fn mdq::md_elem::elem::BlockQuote::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::BlockQuote::borrow(&self) -> &T
pub fn mdq::md_elem::elem::BlockQuote::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::BlockQuote::from(t: T) -> T
pub struct mdq::md_elem::elem::CodeBlock
pub fn mdq::md_elem::elem::CodeBlock::clone(&self) -> mdq::md_elem::elem::CodeBlock
pub fn mdq::md_elem::elem::CodeBlock::eq(&self, other: &mdq::md_elem::elem::CodeBlock) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::CodeBlock) -> Self
pub fn mdq::md_elem::elem::CodeBlock::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::CodeBlock::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::CodeBlock::into(self) -> U
pub type mdq::md_elem::elem::CodeBlock::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::CodeBlock::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::CodeBlock::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::CodeBlock::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::CodeBlock::Owned = T
pub fn mdq::md_elem::elem::CodeBlock::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::CodeBlock::to_owned(&self) -> T
pub fn mdq::md_elem::elem::CodeBlock::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::CodeBlock::borrow(&self) -> &T
pub fn mdq::md_elem::elem::CodeBlock::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::CodeBlock::from(t: T) -> T
pub struct mdq::md_elem::elem::CodeOpts
pub fn mdq::md_elem::elem::CodeOpts::clone(&self) -> mdq::md_elem::elem::CodeOpts
pub fn mdq::md_elem::elem::CodeOpts::eq(&self, other: &mdq::md_elem::elem::CodeOpts) -> bool
pub fn mdq::md_elem::elem::CodeOpts::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::CodeOpts::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::CodeOpts::into(self) -> U
pub type mdq::md_elem::elem::CodeOpts::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::CodeOpts::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::CodeOpts::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::CodeOpts::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::CodeOpts::Owned = T
pub fn mdq::md_elem::elem::CodeOpts::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::CodeOpts::to_owned(&self) -> T
pub fn mdq::md_elem::elem::CodeOpts::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::CodeOpts::borrow(&self) -> &T
pub fn mdq::md_elem::elem::CodeOpts::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::CodeOpts::from(t: T) -> T
pub struct mdq::md_elem::elem::FootnoteId
pub fn mdq::md_elem::elem::FootnoteId::as_str(&self) -> &str
pub fn mdq::md_elem::elem::FootnoteId::clone(&self) -> mdq::md_elem::elem::FootnoteId
pub fn mdq::md_elem::elem::FootnoteId::eq(&self, other: &mdq::md_elem::elem::FootnoteId) -> bool
pub fn mdq::md_elem::elem::FootnoteId::from(id: alloc::string::String) -> Self
pub fn mdq::md_elem::elem::FootnoteId::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::FootnoteId::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::FootnoteId::into(self) -> U
pub type mdq::md_elem::elem::FootnoteId::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::FootnoteId::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::FootnoteId::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::FootnoteId::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::FootnoteId::Owned = T
pub fn mdq::md_elem::elem::FootnoteId::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::FootnoteId::to_owned(&self) -> T
pub fn mdq::md_elem::elem::FootnoteId::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::FootnoteId::borrow(&self) -> &T
pub fn mdq::md_elem::elem::FootnoteId::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::FootnoteId::from(t: T) -> T
pub struct mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::clone(&self) -> mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::eq(&self, other: &mdq::md_elem::elem::FrontMatter) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::FrontMatter) -> Self
pub fn mdq::md_elem::elem::FrontMatter::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::FrontMatter::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::FrontMatter::into(self) -> U
pub type mdq::md_elem::elem::FrontMatter::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::FrontMatter::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::FrontMatter::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::FrontMatter::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::FrontMatter::Owned = T
pub fn mdq::md_elem::elem::FrontMatter::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::FrontMatter::to_owned(&self) -> T
pub fn mdq::md_elem::elem::FrontMatter::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::FrontMatter::borrow(&self) -> &T
pub fn mdq::md_elem::elem::FrontMatter::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::FrontMatter::from(t: T) -> T
pub struct mdq::md_elem::elem::Image
pub fn mdq::md_elem::elem::Image::clone(&self) -> mdq::md_elem::elem::Image
pub fn mdq::md_elem::elem::Image::eq(&self, other: &mdq::md_elem::elem::Image) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Image) -> Self
pub fn mdq::md_elem::elem::Image::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Image::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Image::into(self) -> U
pub type mdq::md_elem::elem::Image::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Image::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::Image::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Image::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::Image::Owned = T
pub fn mdq::md_elem::elem::Image::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Image::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Image::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Image::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Image::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Image::from(t: T) -> T
pub struct mdq::md_elem::elem::Link
pub fn mdq::md_elem::elem::Link::clone(&self) -> mdq::md_elem::elem::Link
pub fn mdq::md_elem::elem::Link::eq(&self, other: &mdq::md_elem::elem::Link) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Link) -> Self
pub fn mdq::md_elem::elem::Link::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Link::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Link::into(self) -> U
pub type mdq::md_elem::elem::Link::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Link::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::Link::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Link::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::Link::Owned = T
pub fn mdq::md_elem::elem::Link::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Link::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Link::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Link::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Link::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Link::from(t: T) -> T
pub struct mdq::md_elem::elem::LinkDefinition
pub fn mdq::md_elem::elem::LinkDefinition::clone(&self) -> mdq::md_elem::elem::LinkDefinition
pub fn mdq::md_elem::elem::LinkDefinition::eq(&self, other: &mdq::md_elem::elem::LinkDefinition) -> bool
pub fn mdq::md_elem::elem::LinkDefinition::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::LinkDefinition::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::LinkDefinition::into(self) -> U
pub type mdq::md_elem::elem::LinkDefinition::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::LinkDefinition::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::LinkDefinition::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::LinkDefinition::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::LinkDefinition::Owned = T
pub fn mdq::md_elem::elem::LinkDefinition::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::LinkDefinition::to_owned(&self) -> T
pub fn mdq::md_elem::elem::LinkDefinition::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::LinkDefinition::borrow(&self) -> &T
pub fn mdq::md_elem::elem::LinkDefinition::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::LinkDefinition::from(t: T) -> T
pub struct mdq::md_elem::elem::List
pub fn mdq::md_elem::elem::List::clone(&self) -> mdq::md_elem::elem::List
pub fn mdq::md_elem::elem::List::eq(&self, other: &mdq::md_elem::elem::List) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::List) -> Self
pub fn mdq::md_elem::elem::List::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::List::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::List::into(self) -> U
pub type mdq::md_elem::elem::List::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::List::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::List::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::List::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::List::Owned = T
pub fn mdq::md_elem::elem::List::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::List::to_owned(&self) -> T
pub fn mdq::md_elem::elem::List::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::List::borrow(&self) -> &T
pub fn mdq::md_elem::elem::List::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::List::from(t: T) -> T
pub struct mdq::md_elem::elem::ListItem
pub fn mdq::md_elem::elem::ListItem::clone(&self) -> mdq::md_elem::elem::ListItem
pub fn mdq::md_elem::elem::ListItem::eq(&self, other: &mdq::md_elem::elem::ListItem) -> bool
pub fn mdq::md_elem::elem::ListItem::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::ListItem::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::ListItem::into(self) -> U
pub type mdq::md_elem::elem::ListItem::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::ListItem::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::ListItem::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::ListItem::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::ListItem::Owned = T
pub fn mdq::md_elem::elem::ListItem::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::ListItem::to_owned(&self) -> T
pub fn mdq::md_elem::elem::ListItem::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::ListItem::borrow(&self) -> &T
pub fn mdq::md_elem::elem::ListItem::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::ListItem::from(t: T) -> T
pub struct mdq::md_elem::elem::Paragraph
pub fn mdq::md_elem::elem::Paragraph::clone(&self) -> mdq::md_elem::elem::Paragraph
pub fn mdq::md_elem::elem::Paragraph::eq(&self, other: &mdq::md_elem::elem::Paragraph) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Paragraph) -> Self
pub fn mdq::md_elem::elem::Paragraph::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Paragraph::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Paragraph::into(self) -> U
pub type mdq::md_elem::elem::Paragraph::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Paragraph::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::Paragraph::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Paragraph::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::Paragraph::Owned = T
pub fn mdq::md_elem::elem::Paragraph::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Paragraph::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Paragraph::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Paragraph::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Paragraph::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Paragraph::from(t: T) -> T
pub struct mdq::md_elem::elem::Section
pub fn mdq::md_elem::elem::Section::clone(&self) -> mdq::md_elem::elem::Section
pub fn mdq::md_elem::elem::Section::eq(&self, other: &mdq::md_elem::elem::Section) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Section) -> Self
pub fn mdq::md_elem::elem::Section::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Section::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Section::into(self) -> U
pub type mdq::md_elem::elem::Section::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Section::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::Section::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Section::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::Section::Owned = T
pub fn mdq::md_elem::elem::Section::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Section::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Section::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Section::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Section::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Section::from(t: T) -> T
pub struct mdq::md_elem::elem::Span
pub fn mdq::md_elem::elem::Span::clone(&self) -> mdq::md_elem::elem::Span
pub fn mdq::md_elem::elem::Span::eq(&self, other: &mdq::md_elem::elem::Span) -> bool
pub fn mdq::md_elem::elem::Span::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Span::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Span::into(self) -> U
pub type mdq::md_elem::elem::Span::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Span::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::Span::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Span::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::Span::Owned = T
pub fn mdq::md_elem::elem::Span::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Span::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Span::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Span::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Span::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Span::from(t: T) -> T
pub struct mdq::md_elem::elem::Table
pub fn mdq::md_elem::elem::Table::alignments(&self) -> &[core::option::Option<mdq::md_elem::elem::ColumnAlignment>]
pub fn mdq::md_elem::elem::Table::is_empty(&self) -> bool
pub fn mdq::md_elem::elem::Table::normalize(&mut self)
pub fn mdq::md_elem::elem::Table::retain_columns_by_header<F, E>(&mut self, f: F) -> core::result::Result<(), E> where F: core::ops::function::FnMut(&mdq::md_elem::elem::TableCell) -> core::result::Result<bool, E>
pub fn mdq::md_elem::elem::Table::retain_rows<F, E>(&mut self, f: F) -> core::result::Result<(), E> where F: core::ops::function::FnMut(&mdq::md_elem::elem::TableCell) -> core::result::Result<bool, E>
pub fn mdq::md_elem::elem::Table::rows(&self) -> &alloc::vec::Vec<mdq::md_elem::elem::TableRow>
pub fn mdq::md_elem::elem::Table::clone(&self) -> mdq::md_elem::elem::Table
pub fn mdq::md_elem::elem::Table::eq(&self, other: &mdq::md_elem::elem::Table) -> bool
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Table) -> Self
pub fn mdq::md_elem::elem::Table::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Table::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Table::into(self) -> U
pub type mdq::md_elem::elem::Table::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Table::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::Table::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Table::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::Table::Owned = T
pub fn mdq::md_elem::elem::Table::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Table::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Table::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Table::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Table::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Table::from(t: T) -> T
pub struct mdq::md_elem::elem::Text
pub fn mdq::md_elem::elem::Text::clone(&self) -> mdq::md_elem::elem::Text
pub fn mdq::md_elem::elem::Text::eq(&self, other: &mdq::md_elem::elem::Text) -> bool
pub fn mdq::md_elem::elem::Text::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::elem::Text::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::elem::Text::into(self) -> U
pub type mdq::md_elem::elem::Text::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Text::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::elem::Text::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Text::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::elem::Text::Owned = T
pub fn mdq::md_elem::elem::Text::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Text::to_owned(&self) -> T
pub fn mdq::md_elem::elem::Text::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::elem::Text::borrow(&self) -> &T
pub fn mdq::md_elem::elem::Text::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::elem::Text::from(t: T) -> T
pub type mdq::md_elem::elem::TableCell = alloc::vec::Vec<mdq::md_elem::elem::Inline>
pub type mdq::md_elem::elem::TableRow = alloc::vec::Vec<mdq::md_elem::elem::TableCell>
pub enum mdq::md_elem::InvalidMd
pub fn mdq::md_elem::InvalidMd::eq(&self, other: &mdq::md_elem::InvalidMd) -> bool
pub fn mdq::md_elem::InvalidMd::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::InvalidMd::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::InvalidMd::into(self) -> U
pub type mdq::md_elem::InvalidMd::Error = core::convert::Infallible
pub fn mdq::md_elem::InvalidMd::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::InvalidMd::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::InvalidMd::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::InvalidMd::to_string(&self) -> alloc::string::String
pub fn mdq::md_elem::InvalidMd::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::InvalidMd::borrow(&self) -> &T
pub fn mdq::md_elem::InvalidMd::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::InvalidMd::from(t: T) -> T
pub enum mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::clone(&self) -> mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::eq(&self, other: &mdq::md_elem::MdElem) -> bool
pub fn mdq::md_elem::MdElem::from(elems: alloc::vec::Vec<mdq::md_elem::MdElem>) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockHtml) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockQuote) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::CodeBlock) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::FrontMatter) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Image) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Inline) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Link) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::List) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Paragraph) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Section) -> Self
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Table) -> Self
pub fn mdq::md_elem::MdElem::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::MdElem::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::md_elem::MdElem::into(self) -> U
pub type mdq::md_elem::MdElem::Error = core::convert::Infallible
pub fn mdq::md_elem::MdElem::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::MdElem::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::MdElem::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::MdElem::Owned = T
pub fn mdq::md_elem::MdElem::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MdElem::to_owned(&self) -> T
pub fn mdq::md_elem::MdElem::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::MdElem::borrow(&self) -> &T
pub fn mdq::md_elem::MdElem::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::MdElem::from(t: T) -> T
pub struct mdq::md_elem::MarkdownPart
pub fn mdq::md_elem::MarkdownPart::clone(&self) -> mdq::md_elem::MarkdownPart
pub fn mdq::md_elem::MarkdownPart::eq(&self, other: &mdq::md_elem::MarkdownPart) -> bool
pub fn mdq::md_elem::MarkdownPart::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::MarkdownPart::into(self) -> U
pub type mdq::md_elem::MarkdownPart::Error = core::convert::Infallible
pub fn mdq::md_elem::MarkdownPart::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::MarkdownPart::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::MarkdownPart::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::MarkdownPart::Owned = T
pub fn mdq::md_elem::MarkdownPart::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MarkdownPart::to_owned(&self) -> T
pub fn mdq::md_elem::MarkdownPart::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::MarkdownPart::borrow(&self) -> &T
pub fn mdq::md_elem::MarkdownPart::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::MarkdownPart::from(t: T) -> T
pub struct mdq::md_elem::MdContext
pub fn mdq::md_elem::MdContext::get_footnote(&self, footnote_id: &mdq::md_elem::elem::FootnoteId) -> &alloc::vec::Vec<mdq::md_elem::MdElem>
pub fn mdq::md_elem::MdContext::clone(&self) -> mdq::md_elem::MdContext
pub fn mdq::md_elem::MdContext::eq(&self, other: &mdq::md_elem::MdContext) -> bool
pub fn mdq::md_elem::MdContext::default() -> mdq::md_elem::MdContext
pub fn mdq::md_elem::MdContext::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::MdContext::into(self) -> U
pub type mdq::md_elem::MdContext::Error = core::convert::Infallible
pub fn mdq::md_elem::MdContext::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::MdContext::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::MdContext::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::MdContext::Owned = T
pub fn mdq::md_elem::MdContext::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MdContext::to_owned(&self) -> T
pub fn mdq::md_elem::MdContext::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::MdContext::borrow(&self) -> &T
pub fn mdq::md_elem::MdContext::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::MdContext::from(t: T) -> T
pub struct mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::parse(text: &str, options: &mdq::md_elem::ParseOptions) -> core::result::Result<Self, mdq::md_elem::InvalidMd>
pub fn mdq::md_elem::MdDoc::clone(&self) -> mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::eq(&self, other: &mdq::md_elem::MdDoc) -> bool
pub fn mdq::md_elem::MdDoc::default() -> mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::MdDoc::into(self) -> U
pub type mdq::md_elem::MdDoc::Error = core::convert::Infallible
pub fn mdq::md_elem::MdDoc::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::MdDoc::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::MdDoc::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::md_elem::MdDoc::Owned = T
pub fn mdq::md_elem::MdDoc::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MdDoc::to_owned(&self) -> T
pub fn mdq::md_elem::MdDoc::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::MdDoc::borrow(&self) -> &T
pub fn mdq::md_elem::MdDoc::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::MdDoc::from(t: T) -> T
pub struct mdq::md_elem::ParseOptions
pub fn mdq::md_elem::ParseOptions::gfm() -> Self
pub fn mdq::md_elem::ParseOptions::default() -> Self
pub fn mdq::md_elem::ParseOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::ParseOptions::into(self) -> U
pub type mdq::md_elem::ParseOptions::Error = core::convert::Infallible
pub fn mdq::md_elem::ParseOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::ParseOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::ParseOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::ParseOptions::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::ParseOptions::borrow(&self) -> &T
pub fn mdq::md_elem::ParseOptions::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::ParseOptions::from(t: T) -> T
pub struct mdq::md_elem::UnknownMdParseError
pub fn mdq::md_elem::UnknownMdParseError::eq(&self, _other: &Self) -> bool
pub fn mdq::md_elem::UnknownMdParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::UnknownMdParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::md_elem::UnknownMdParseError::into(self) -> U
pub type mdq::md_elem::UnknownMdParseError::Error = core::convert::Infallible
pub fn mdq::md_elem::UnknownMdParseError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::md_elem::UnknownMdParseError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::UnknownMdParseError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::md_elem::UnknownMdParseError::to_string(&self) -> alloc::string::String
pub fn mdq::md_elem::UnknownMdParseError::type_id(&self) -> core::any::TypeId
pub fn mdq::md_elem::UnknownMdParseError::borrow(&self) -> &T
pub fn mdq::md_elem::UnknownMdParseError::borrow_mut(&mut self) -> &mut T
pub fn mdq::md_elem::UnknownMdParseError::from(t: T) -> T
pub mod mdq::output
pub fn mdq::output::InlineElemOptionsBuilderError::from(s: alloc::string::String) -> Self
pub fn mdq::output::InlineElemOptionsBuilderError::from(s: derive_builder::error::UninitializedFieldError) -> Self
pub fn mdq::output::InlineElemOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::InlineElemOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::InlineElemOptionsBuilderError::into(self) -> U
pub type mdq::output::InlineElemOptionsBuilderError::Error = core::convert::Infallible
pub fn mdq::output::InlineElemOptionsBuilderError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::InlineElemOptionsBuilderError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::InlineElemOptionsBuilderError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::output::InlineElemOptionsBuilderError::to_string(&self) -> alloc::string::String
pub fn mdq::output::InlineElemOptionsBuilderError::type_id(&self) -> core::any::TypeId
pub fn mdq::output::InlineElemOptionsBuilderError::borrow(&self) -> &T
pub fn mdq::output::InlineElemOptionsBuilderError::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::InlineElemOptionsBuilderError::from(t: T) -> T
pub enum mdq::output::LinkTransform
pub fn mdq::output::LinkTransform::to_possible_value<'a>(&self) -> core::option::Option<clap_builder::builder::possible_value::PossibleValue>
pub fn mdq::output::LinkTransform::value_variants<'a>() -> &'a [Self]
pub fn mdq::output::LinkTransform::clone(&self) -> mdq::output::LinkTransform
pub fn mdq::output::LinkTransform::cmp(&self, other: &mdq::output::LinkTransform) -> core::cmp::Ordering
pub fn mdq::output::LinkTransform::eq(&self, other: &mdq::output::LinkTransform) -> bool
pub fn mdq::output::LinkTransform::partial_cmp(&self, other: &mdq::output::LinkTransform) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::output::LinkTransform::default() -> mdq::output::LinkTransform
pub fn mdq::output::LinkTransform::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::LinkTransform::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::output::LinkTransform::into(self) -> U
pub type mdq::output::LinkTransform::Error = core::convert::Infallible
pub fn mdq::output::LinkTransform::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::LinkTransform::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::LinkTransform::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::output::LinkTransform::Owned = T
pub fn mdq::output::LinkTransform::clone_into(&self, target: &mut T)
pub fn mdq::output::LinkTransform::to_owned(&self) -> T
pub fn mdq::output::LinkTransform::type_id(&self) -> core::any::TypeId
pub fn mdq::output::LinkTransform::borrow(&self) -> &T
pub fn mdq::output::LinkTransform::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::LinkTransform::from(t: T) -> T
pub fn mdq::output::MdWriterOptionsBuilderError::from(s: alloc::string::String) -> Self
pub fn mdq::output::MdWriterOptionsBuilderError::from(s: derive_builder::error::UninitializedFieldError) -> Self
pub fn mdq::output::MdWriterOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::MdWriterOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::MdWriterOptionsBuilderError::into(self) -> U
pub type mdq::output::MdWriterOptionsBuilderError::Error = core::convert::Infallible
pub fn mdq::output::MdWriterOptionsBuilderError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::MdWriterOptionsBuilderError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::MdWriterOptionsBuilderError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::output::MdWriterOptionsBuilderError::to_string(&self) -> alloc::string::String
pub fn mdq::output::MdWriterOptionsBuilderError::type_id(&self) -> core::any::TypeId
pub fn mdq::output::MdWriterOptionsBuilderError::borrow(&self) -> &T
pub fn mdq::output::MdWriterOptionsBuilderError::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::MdWriterOptionsBuilderError::from(t: T) -> T
pub enum mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::to_possible_value<'a>(&self) -> core::option::Option<clap_builder::builder::possible_value::PossibleValue>
pub fn mdq::output::ReferencePlacement::value_variants<'a>() -> &'a [Self]
pub fn mdq::output::ReferencePlacement::clone(&self) -> mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::cmp(&self, other: &mdq::output::ReferencePlacement) -> core::cmp::Ordering
pub fn mdq::output::ReferencePlacement::eq(&self, other: &mdq::output::ReferencePlacement) -> bool
pub fn mdq::output::ReferencePlacement::partial_cmp(&self, other: &mdq::output::ReferencePlacement) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::output::ReferencePlacement::default() -> mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::ReferencePlacement::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::output::ReferencePlacement::into(self) -> U
pub type mdq::output::ReferencePlacement::Error = core::convert::Infallible
pub fn mdq::output::ReferencePlacement::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::ReferencePlacement::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::ReferencePlacement::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::output::ReferencePlacement::Owned = T
pub fn mdq::output::ReferencePlacement::clone_into(&self, target: &mut T)
pub fn mdq::output::ReferencePlacement::to_owned(&self) -> T
pub fn mdq::output::ReferencePlacement::type_id(&self) -> core::any::TypeId
pub fn mdq::output::ReferencePlacement::borrow(&self) -> &T
pub fn mdq::output::ReferencePlacement::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::ReferencePlacement::from(t: T) -> T
pub struct mdq::output::InlineElemOptions
pub fn mdq::output::InlineElemOptions::clone(&self) -> mdq::output::InlineElemOptions
pub fn mdq::output::InlineElemOptions::cmp(&self, other: &mdq::output::InlineElemOptions) -> core::cmp::Ordering
pub fn mdq::output::InlineElemOptions::eq(&self, other: &mdq::output::InlineElemOptions) -> bool
pub fn mdq::output::InlineElemOptions::partial_cmp(&self, other: &mdq::output::InlineElemOptions) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::output::InlineElemOptions::default() -> mdq::output::InlineElemOptions
pub fn mdq::output::InlineElemOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::InlineElemOptions::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::output::InlineElemOptions::into(self) -> U
pub type mdq::output::InlineElemOptions::Error = core::convert::Infallible
pub fn mdq::output::InlineElemOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::InlineElemOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::InlineElemOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::output::InlineElemOptions::Owned = T
pub fn mdq::output::InlineElemOptions::clone_into(&self, target: &mut T)
pub fn mdq::output::InlineElemOptions::to_owned(&self) -> T
pub fn mdq::output::InlineElemOptions::type_id(&self) -> core::any::TypeId
pub fn mdq::output::InlineElemOptions::borrow(&self) -> &T
pub fn mdq::output::InlineElemOptions::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::InlineElemOptions::from(t: T) -> T
pub struct mdq::output::InlineElemOptionsBuilder
pub fn mdq::output::InlineElemOptionsBuilder::build(&self) -> core::result::Result<mdq::output::InlineElemOptions, mdq::output::InlineElemOptionsBuilderError>
pub fn mdq::output::InlineElemOptionsBuilder::link_format(&mut self, value: mdq::output::LinkTransform) -> &mut Self
pub fn mdq::output::InlineElemOptionsBuilder::renumber_footnotes(&mut self, value: bool) -> &mut Self
pub fn mdq::output::InlineElemOptionsBuilder::clone(&self) -> mdq::output::InlineElemOptionsBuilder
pub fn mdq::output::InlineElemOptionsBuilder::default() -> Self
pub fn mdq::output::InlineElemOptionsBuilder::into(self) -> U
pub type mdq::output::InlineElemOptionsBuilder::Error = core::convert::Infallible
pub fn mdq::output::InlineElemOptionsBuilder::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::InlineElemOptionsBuilder::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::InlineElemOptionsBuilder::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::output::InlineElemOptionsBuilder::Owned = T
pub fn mdq::output::InlineElemOptionsBuilder::clone_into(&self, target: &mut T)
pub fn mdq::output::InlineElemOptionsBuilder::to_owned(&self) -> T
pub fn mdq::output::InlineElemOptionsBuilder::type_id(&self) -> core::any::TypeId
pub fn mdq::output::InlineElemOptionsBuilder::borrow(&self) -> &T
pub fn mdq::output::InlineElemOptionsBuilder::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::InlineElemOptionsBuilder::from(t: T) -> T
pub struct mdq::output::IoAdapter<W>(pub W)
pub fn mdq::output::IoAdapter<W>::clone(&self) -> mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::cmp(&self, other: &mdq::output::IoAdapter<W>) -> core::cmp::Ordering
pub fn mdq::output::IoAdapter<W>::eq(&self, other: &mdq::output::IoAdapter<W>) -> bool
pub fn mdq::output::IoAdapter<W>::partial_cmp(&self, other: &mdq::output::IoAdapter<W>) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::output::IoAdapter<W>::default() -> mdq::output::IoAdapter<W>
pub fn mdq::output::IoAdapter<W>::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::IoAdapter<W>::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::output::IoAdapter<W>::write_str(&mut self, s: &str) -> core::fmt::Result
pub fn mdq::output::IoAdapter<W>::from(value: W) -> Self
pub fn mdq::output::IoAdapter<W>::into(self) -> U
pub type mdq::output::IoAdapter<W>::Error = core::convert::Infallible
pub fn mdq::output::IoAdapter<W>::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::IoAdapter<W>::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::IoAdapter<W>::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::output::IoAdapter<W>::Owned = T
pub fn mdq::output::IoAdapter<W>::clone_into(&self, target: &mut T)
pub fn mdq::output::IoAdapter<W>::to_owned(&self) -> T
pub fn mdq::output::IoAdapter<W>::type_id(&self) -> core::any::TypeId
pub fn mdq::output::IoAdapter<W>::borrow(&self) -> &T
pub fn mdq::output::IoAdapter<W>::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::IoAdapter<W>::from(t: T) -> T
pub fn mdq::output::IoAdapter<W>::from(t: never) -> T
pub struct mdq::output::MdWriter
pub fn mdq::output::MdWriter::with_options(options: mdq::output::MdWriterOptions) -> Self
pub fn mdq::output::MdWriter::write<'md, I, W>(&self, ctx: &'md mdq::md_elem::MdContext, nodes: I, out: &mut W) where I: core::iter::traits::collect::IntoIterator<Item = &'md mdq::md_elem::MdElem>, W: core::fmt::Write
pub fn mdq::output::MdWriter::clone(&self) -> mdq::output::MdWriter
pub fn mdq::output::MdWriter::cmp(&self, other: &mdq::output::MdWriter) -> core::cmp::Ordering
pub fn mdq::output::MdWriter::eq(&self, other: &mdq::output::MdWriter) -> bool
pub fn mdq::output::MdWriter::partial_cmp(&self, other: &mdq::output::MdWriter) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::output::MdWriter::default() -> mdq::output::MdWriter
pub fn mdq::output::MdWriter::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::MdWriter::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::output::MdWriter::into(self) -> U
pub type mdq::output::MdWriter::Error = core::convert::Infallible
pub fn mdq::output::MdWriter::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::MdWriter::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::MdWriter::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::output::MdWriter::Owned = T
pub fn mdq::output::MdWriter::clone_into(&self, target: &mut T)
pub fn mdq::output::MdWriter::to_owned(&self) -> T
pub fn mdq::output::MdWriter::type_id(&self) -> core::any::TypeId
pub fn mdq::output::MdWriter::borrow(&self) -> &T
pub fn mdq::output::MdWriter::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::MdWriter::from(t: T) -> T
pub struct mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::clone(&self) -> mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::cmp(&self, other: &mdq::output::MdWriterOptions) -> core::cmp::Ordering
pub fn mdq::output::MdWriterOptions::eq(&self, other: &mdq::output::MdWriterOptions) -> bool
pub fn mdq::output::MdWriterOptions::partial_cmp(&self, other: &mdq::output::MdWriterOptions) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::output::MdWriterOptions::from(cli: &mdq::run::RunOptions) -> Self
pub fn mdq::output::MdWriterOptions::default() -> mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::MdWriterOptions::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::output::MdWriterOptions::into(self) -> U
pub type mdq::output::MdWriterOptions::Error = core::convert::Infallible
pub fn mdq::output::MdWriterOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::MdWriterOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::MdWriterOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::output::MdWriterOptions::Owned = T
pub fn mdq::output::MdWriterOptions::clone_into(&self, target: &mut T)
pub fn mdq::output::MdWriterOptions::to_owned(&self) -> T
pub fn mdq::output::MdWriterOptions::type_id(&self) -> core::any::TypeId
pub fn mdq::output::MdWriterOptions::borrow(&self) -> &T
pub fn mdq::output::MdWriterOptions::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::MdWriterOptions::from(t: T) -> T
pub struct mdq::output::MdWriterOptionsBuilder
pub fn mdq::output::MdWriterOptionsBuilder::build(&self) -> core::result::Result<mdq::output::MdWriterOptions, mdq::output::MdWriterOptionsBuilderError>
pub fn mdq::output::MdWriterOptionsBuilder::footnote_reference_placement(&mut self, value: mdq::output::ReferencePlacement) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::include_thematic_breaks(&mut self, value: bool) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::inline_options(&mut self, value: mdq::output::InlineElemOptions) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::link_reference_placement(&mut self, value: mdq::output::ReferencePlacement) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::text_width(&mut self, value: core::option::Option<usize>) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::clone(&self) -> mdq::output::MdWriterOptionsBuilder
pub fn mdq::output::MdWriterOptionsBuilder::default() -> Self
pub fn mdq::output::MdWriterOptionsBuilder::into(self) -> U
pub type mdq::output::MdWriterOptionsBuilder::Error = core::convert::Infallible
pub fn mdq::output::MdWriterOptionsBuilder::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::MdWriterOptionsBuilder::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::MdWriterOptionsBuilder::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::output::MdWriterOptionsBuilder::Owned = T
pub fn mdq::output::MdWriterOptionsBuilder::clone_into(&self, target: &mut T)
pub fn mdq::output::MdWriterOptionsBuilder::to_owned(&self) -> T
pub fn mdq::output::MdWriterOptionsBuilder::type_id(&self) -> core::any::TypeId
pub fn mdq::output::MdWriterOptionsBuilder::borrow(&self) -> &T
pub fn mdq::output::MdWriterOptionsBuilder::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::MdWriterOptionsBuilder::from(t: T) -> T
pub struct mdq::output::PlainWriter
pub fn mdq::output::PlainWriter::with_options(options: mdq::output::PlainWriterOptions) -> Self
pub fn mdq::output::PlainWriter::write<'md, I, W>(&self, nodes: I, out: &mut W) where I: core::iter::traits::collect::IntoIterator<Item = &'md mdq::md_elem::MdElem>, W: std::io::Write
pub fn mdq::output::PlainWriter::clone(&self) -> mdq::output::PlainWriter
pub fn mdq::output::PlainWriter::cmp(&self, other: &mdq::output::PlainWriter) -> core::cmp::Ordering
pub fn mdq::output::PlainWriter::eq(&self, other: &mdq::output::PlainWriter) -> bool
pub fn mdq::output::PlainWriter::partial_cmp(&self, other: &mdq::output::PlainWriter) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::output::PlainWriter::default() -> mdq::output::PlainWriter
pub fn mdq::output::PlainWriter::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::PlainWriter::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::output::PlainWriter::into(self) -> U
pub type mdq::output::PlainWriter::Error = core::convert::Infallible
pub fn mdq::output::PlainWriter::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::PlainWriter::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::PlainWriter::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::output::PlainWriter::Owned = T
pub fn mdq::output::PlainWriter::clone_into(&self, target: &mut T)
pub fn mdq::output::PlainWriter::to_owned(&self) -> T
pub fn mdq::output::PlainWriter::type_id(&self) -> core::any::TypeId
pub fn mdq::output::PlainWriter::borrow(&self) -> &T
pub fn mdq::output::PlainWriter::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::PlainWriter::from(t: T) -> T
pub struct mdq::output::PlainWriterOptions
pub fn mdq::output::PlainWriterOptions::clone(&self) -> mdq::output::PlainWriterOptions
pub fn mdq::output::PlainWriterOptions::cmp(&self, other: &mdq::output::PlainWriterOptions) -> core::cmp::Ordering
pub fn mdq::output::PlainWriterOptions::eq(&self, other: &mdq::output::PlainWriterOptions) -> bool
pub fn mdq::output::PlainWriterOptions::partial_cmp(&self, other: &mdq::output::PlainWriterOptions) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::output::PlainWriterOptions::default() -> mdq::output::PlainWriterOptions
pub fn mdq::output::PlainWriterOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::PlainWriterOptions::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::output::PlainWriterOptions::into(self) -> U
pub type mdq::output::PlainWriterOptions::Error = core::convert::Infallible
pub fn mdq::output::PlainWriterOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::PlainWriterOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::PlainWriterOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::output::PlainWriterOptions::Owned = T
pub fn mdq::output::PlainWriterOptions::clone_into(&self, target: &mut T)
pub fn mdq::output::PlainWriterOptions::to_owned(&self) -> T
pub fn mdq::output::PlainWriterOptions::type_id(&self) -> core::any::TypeId
pub fn mdq::output::PlainWriterOptions::borrow(&self) -> &T
pub fn mdq::output::PlainWriterOptions::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::PlainWriterOptions::from(t: T) -> T
pub struct mdq::output::SerializableMd<'md>
pub fn mdq::output::SerializableMd<'md>::new(elems: &'md [mdq::md_elem::MdElem], ctx: &'md mdq::md_elem::MdContext, opts: mdq::output::InlineElemOptions) -> Self
pub fn mdq::output::SerializableMd<'md>::clone(&self) -> mdq::output::SerializableMd<'md>
pub fn mdq::output::SerializableMd<'md>::default() -> mdq::output::SerializableMd<'md>
pub fn mdq::output::SerializableMd<'md>::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::output::SerializableMd<'md>::serialize<__S>(&self, __serializer: __S) -> core::result::Result<<__S as serde::ser::Serializer>::Ok, <__S as serde::ser::Serializer>::Error> where __S: serde::ser::Serializer
pub fn mdq::output::SerializableMd<'md>::into(self) -> U
pub type mdq::output::SerializableMd<'md>::Error = core::convert::Infallible
pub fn mdq::output::SerializableMd<'md>::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::output::SerializableMd<'md>::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::SerializableMd<'md>::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::output::SerializableMd<'md>::Owned = T
pub fn mdq::output::SerializableMd<'md>::clone_into(&self, target: &mut T)
pub fn mdq::output::SerializableMd<'md>::to_owned(&self) -> T
pub fn mdq::output::SerializableMd<'md>::type_id(&self) -> core::any::TypeId
pub fn mdq::output::SerializableMd<'md>::borrow(&self) -> &T
pub fn mdq::output::SerializableMd<'md>::borrow_mut(&mut self) -> &mut T
pub fn mdq::output::SerializableMd<'md>::from(t: T) -> T
pub mod mdq::run
pub enum mdq::run::Error
pub fn mdq::run::Error::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::run::Error::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::run::Error::into(self) -> U
pub type mdq::run::Error::Error = core::convert::Infallible
pub fn mdq::run::Error::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::run::Error::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::Error::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::run::Error::to_string(&self) -> alloc::string::String
pub fn mdq::run::Error::type_id(&self) -> core::any::TypeId
pub fn mdq::run::Error::borrow(&self) -> &T
pub fn mdq::run::Error::borrow_mut(&mut self) -> &mut T
pub fn mdq::run::Error::from(t: T) -> T
pub enum mdq::run::Input
pub fn mdq::run::Input::clone(&self) -> mdq::run::Input
pub fn mdq::run::Input::cmp(&self, other: &mdq::run::Input) -> core::cmp::Ordering
pub fn mdq::run::Input::eq(&self, other: &mdq::run::Input) -> bool
pub fn mdq::run::Input::partial_cmp(&self, other: &mdq::run::Input) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::run::Input::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::run::Input::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::run::Input::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::run::Input::into(self) -> U
pub type mdq::run::Input::Error = core::convert::Infallible
pub fn mdq::run::Input::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::run::Input::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::Input::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::run::Input::Owned = T
pub fn mdq::run::Input::clone_into(&self, target: &mut T)
pub fn mdq::run::Input::to_owned(&self) -> T
pub fn mdq::run::Input::to_string(&self) -> alloc::string::String
pub fn mdq::run::Input::type_id(&self) -> core::any::TypeId
pub fn mdq::run::Input::borrow(&self) -> &T
pub fn mdq::run::Input::borrow_mut(&mut self) -> &mut T
pub fn mdq::run::Input::from(t: T) -> T
pub enum mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::to_possible_value<'a>(&self) -> core::option::Option<clap_builder::builder::possible_value::PossibleValue>
pub fn mdq::run::OutputFormat::value_variants<'a>() -> &'a [Self]
pub fn mdq::run::OutputFormat::clone(&self) -> mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::cmp(&self, other: &mdq::run::OutputFormat) -> core::cmp::Ordering
pub fn mdq::run::OutputFormat::eq(&self, other: &mdq::run::OutputFormat) -> bool
pub fn mdq::run::OutputFormat::partial_cmp(&self, other: &mdq::run::OutputFormat) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::run::OutputFormat::default() -> Self
pub fn mdq::run::OutputFormat::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::run::OutputFormat::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::run::OutputFormat::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::run::OutputFormat::into(self) -> U
pub type mdq::run::OutputFormat::Error = core::convert::Infallible
pub fn mdq::run::OutputFormat::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::run::OutputFormat::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::OutputFormat::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::run::OutputFormat::Owned = T
pub fn mdq::run::OutputFormat::clone_into(&self, target: &mut T)
pub fn mdq::run::OutputFormat::to_owned(&self) -> T
pub fn mdq::run::OutputFormat::to_string(&self) -> alloc::string::String
pub fn mdq::run::OutputFormat::type_id(&self) -> core::any::TypeId
pub fn mdq::run::OutputFormat::borrow(&self) -> &T
pub fn mdq::run::OutputFormat::borrow_mut(&mut self) -> &mut T
pub fn mdq::run::OutputFormat::from(t: T) -> T
pub fn mdq::run::RunOptionsBuilderError::from(s: alloc::string::String) -> Self
pub fn mdq::run::RunOptionsBuilderError::from(s: derive_builder::error::UninitializedFieldError) -> Self
pub fn mdq::run::RunOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::run::RunOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::run::RunOptionsBuilderError::into(self) -> U
pub type mdq::run::RunOptionsBuilderError::Error = core::convert::Infallible
pub fn mdq::run::RunOptionsBuilderError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::run::RunOptionsBuilderError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::RunOptionsBuilderError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::run::RunOptionsBuilderError::to_string(&self) -> alloc::string::String
pub fn mdq::run::RunOptionsBuilderError::type_id(&self) -> core::any::TypeId
pub fn mdq::run::RunOptionsBuilderError::borrow(&self) -> &T
pub fn mdq::run::RunOptionsBuilderError::borrow_mut(&mut self) -> &mut T
pub fn mdq::run::RunOptionsBuilderError::from(t: T) -> T
pub struct mdq::run::QueryParseError
pub fn mdq::run::QueryParseError::clone(&self) -> mdq::run::QueryParseError
pub fn mdq::run::QueryParseError::eq(&self, other: &mdq::run::QueryParseError) -> bool
pub fn mdq::run::QueryParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::run::QueryParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::run::QueryParseError::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::run::QueryParseError::into(self) -> U
pub type mdq::run::QueryParseError::Error = core::convert::Infallible
pub fn mdq::run::QueryParseError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::run::QueryParseError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::QueryParseError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::run::QueryParseError::Owned = T
pub fn mdq::run::QueryParseError::clone_into(&self, target: &mut T)
pub fn mdq::run::QueryParseError::to_owned(&self) -> T
pub fn mdq::run::QueryParseError::to_string(&self) -> alloc::string::String
pub fn mdq::run::QueryParseError::type_id(&self) -> core::any::TypeId
pub fn mdq::run::QueryParseError::borrow(&self) -> &T
pub fn mdq::run::QueryParseError::borrow_mut(&mut self) -> &mut T
pub fn mdq::run::QueryParseError::from(t: T) -> T
pub struct mdq::run::RunOptions
pub fn mdq::run::RunOptions::should_add_breaks(&self) -> bool
pub fn mdq::run::RunOptions::clone(&self) -> mdq::run::RunOptions
pub fn mdq::run::RunOptions::cmp(&self, other: &mdq::run::RunOptions) -> core::cmp::Ordering
pub fn mdq::run::RunOptions::eq(&self, other: &mdq::run::RunOptions) -> bool
pub fn mdq::run::RunOptions::partial_cmp(&self, other: &mdq::run::RunOptions) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::output::MdWriterOptions::from(cli: &mdq::run::RunOptions) -> Self
pub fn mdq::run::RunOptions::default() -> Self
pub fn mdq::run::RunOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::run::RunOptions::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::run::RunOptions::into(self) -> U
pub type mdq::run::RunOptions::Error = core::convert::Infallible
pub fn mdq::run::RunOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::run::RunOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::RunOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::run::RunOptions::Owned = T
pub fn mdq::run::RunOptions::clone_into(&self, target: &mut T)
pub fn mdq::run::RunOptions::to_owned(&self) -> T
pub fn mdq::run::RunOptions::type_id(&self) -> core::any::TypeId
pub fn mdq::run::RunOptions::borrow(&self) -> &T
pub fn mdq::run::RunOptions::borrow_mut(&mut self) -> &mut T
pub fn mdq::run::RunOptions::from(t: T) -> T
pub struct mdq::run::RunOptionsBuilder
pub fn mdq::run::RunOptionsBuilder::add_breaks(&mut self, value: core::option::Option<bool>) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::allow_unknown_markdown(&mut self, value: bool) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::build(&self) -> core::result::Result<mdq::run::RunOptions, mdq::run::RunOptionsBuilderError>
pub fn mdq::run::RunOptionsBuilder::footnote_pos(&mut self, value: core::option::Option<mdq::output::ReferencePlacement>) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::link_format(&mut self, value: mdq::output::LinkTransform) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::link_pos(&mut self, value: mdq::output::ReferencePlacement) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::markdown_file_paths(&mut self, value: alloc::vec::Vec<alloc::string::String>) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::output(&mut self, value: mdq::run::OutputFormat) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::quiet(&mut self, value: bool) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::renumber_footnotes(&mut self, value: bool) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::selectors(&mut self, value: alloc::string::String) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::wrap_width(&mut self, value: core::option::Option<usize>) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::clone(&self) -> mdq::run::RunOptionsBuilder
pub fn mdq::run::RunOptionsBuilder::default() -> Self
pub fn mdq::run::RunOptionsBuilder::into(self) -> U
pub type mdq::run::RunOptionsBuilder::Error = core::convert::Infallible
pub fn mdq::run::RunOptionsBuilder::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::run::RunOptionsBuilder::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::RunOptionsBuilder::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::run::RunOptionsBuilder::Owned = T
pub fn mdq::run::RunOptionsBuilder::clone_into(&self, target: &mut T)
pub fn mdq::run::RunOptionsBuilder::to_owned(&self) -> T
pub fn mdq::run::RunOptionsBuilder::type_id(&self) -> core::any::TypeId
pub fn mdq::run::RunOptionsBuilder::borrow(&self) -> &T
pub fn mdq::run::RunOptionsBuilder::borrow_mut(&mut self) -> &mut T
pub fn mdq::run::RunOptionsBuilder::from(t: T) -> T
pub fn mdq::run::OsFacade::read_all(&self, markdown_file_paths: &[alloc::string::String]) -> core::result::Result<alloc::string::String, mdq::run::Error>
pub fn mdq::run::OsFacade::read_file(&self, path: &str) -> std::io::error::Result<alloc::string::String>
pub fn mdq::run::OsFacade::read_stdin(&self) -> std::io::error::Result<alloc::string::String>
pub fn mdq::run::OsFacade::stdout(&mut self) -> impl std::io::Write
pub fn mdq::run::OsFacade::write_error(&mut self, err: mdq::run::Error)
pub fn mdq::run::run(cli: &mdq::run::RunOptions, os: &mut impl mdq::run::OsFacade) -> bool
pub mod mdq::select
pub enum mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::clone(&self) -> mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::cmp(&self, other: &mdq::select::ListItemTask) -> core::cmp::Ordering
pub fn mdq::select::ListItemTask::eq(&self, other: &mdq::select::ListItemTask) -> bool
pub fn mdq::select::ListItemTask::partial_cmp(&self, other: &mdq::select::ListItemTask) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::ListItemTask::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::ListItemTask::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::ListItemTask::into(self) -> U
pub type mdq::select::ListItemTask::Error = core::convert::Infallible
pub fn mdq::select::ListItemTask::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::ListItemTask::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::ListItemTask::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::ListItemTask::Owned = T
pub fn mdq::select::ListItemTask::clone_into(&self, target: &mut T)
pub fn mdq::select::ListItemTask::to_owned(&self) -> T
pub fn mdq::select::ListItemTask::type_id(&self) -> core::any::TypeId
pub fn mdq::select::ListItemTask::borrow(&self) -> &T
pub fn mdq::select::ListItemTask::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::ListItemTask::from(t: T) -> T
pub enum mdq::select::Matcher
pub fn mdq::select::Matcher::clone(&self) -> mdq::select::Matcher
pub fn mdq::select::Matcher::cmp(&self, other: &mdq::select::Matcher) -> core::cmp::Ordering
pub fn mdq::select::Matcher::eq(&self, other: &mdq::select::Matcher) -> bool
pub fn mdq::select::Matcher::partial_cmp(&self, other: &mdq::select::Matcher) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::Matcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::Matcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::Matcher::into(self) -> U
pub type mdq::select::Matcher::Error = core::convert::Infallible
pub fn mdq::select::Matcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::Matcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::Matcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::Matcher::Owned = T
pub fn mdq::select::Matcher::clone_into(&self, target: &mut T)
pub fn mdq::select::Matcher::to_owned(&self) -> T
pub fn mdq::select::Matcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::Matcher::borrow(&self) -> &T
pub fn mdq::select::Matcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::Matcher::from(t: T) -> T
pub fn mdq::select::Selector::find_nodes(self, doc: mdq::md_elem::MdDoc) -> mdq::select::Result<(alloc::vec::Vec<mdq::md_elem::MdElem>, mdq::md_elem::MdContext)>
pub fn mdq::select::Selector::clone(&self) -> mdq::select::Selector
pub fn mdq::select::Selector::cmp(&self, other: &mdq::select::Selector) -> core::cmp::Ordering
pub fn mdq::select::Selector::eq(&self, other: &mdq::select::Selector) -> bool
pub fn mdq::select::Selector::partial_cmp(&self, other: &mdq::select::Selector) -> core::option::Option<core::cmp::Ordering>
pub type mdq::select::Selector::Error = mdq::select::ParseError
pub fn mdq::select::Selector::try_from(value: &alloc::string::String) -> core::result::Result<Self, Self::Error>
pub type mdq::select::Selector::Error = mdq::select::ParseError
pub fn mdq::select::Selector::try_from(value: &str) -> core::result::Result<Self, Self::Error>
pub fn mdq::select::Selector::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::Selector::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::Selector::into(self) -> U
pub type mdq::select::Selector::Error = core::convert::Infallible
pub fn mdq::select::Selector::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::Selector::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::Selector::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::Selector::Owned = T
pub fn mdq::select::Selector::clone_into(&self, target: &mut T)
pub fn mdq::select::Selector::to_owned(&self) -> T
pub fn mdq::select::Selector::type_id(&self) -> core::any::TypeId
pub fn mdq::select::Selector::borrow(&self) -> &T
pub fn mdq::select::Selector::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::Selector::from(t: T) -> T
pub struct mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::clone(&self) -> mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::cmp(&self, other: &mdq::select::BlockQuoteMatcher) -> core::cmp::Ordering
pub fn mdq::select::BlockQuoteMatcher::eq(&self, other: &mdq::select::BlockQuoteMatcher) -> bool
pub fn mdq::select::BlockQuoteMatcher::partial_cmp(&self, other: &mdq::select::BlockQuoteMatcher) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::BlockQuoteMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::BlockQuoteMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::BlockQuoteMatcher::into(self) -> U
pub type mdq::select::BlockQuoteMatcher::Error = core::convert::Infallible
pub fn mdq::select::BlockQuoteMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::BlockQuoteMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::BlockQuoteMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::BlockQuoteMatcher::Owned = T
pub fn mdq::select::BlockQuoteMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::BlockQuoteMatcher::to_owned(&self) -> T
pub fn mdq::select::BlockQuoteMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::BlockQuoteMatcher::borrow(&self) -> &T
pub fn mdq::select::BlockQuoteMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::BlockQuoteMatcher::from(t: T) -> T
pub struct mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::clone(&self) -> mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::cmp(&self, other: &mdq::select::CodeBlockMatcher) -> core::cmp::Ordering
pub fn mdq::select::CodeBlockMatcher::eq(&self, other: &mdq::select::CodeBlockMatcher) -> bool
pub fn mdq::select::CodeBlockMatcher::partial_cmp(&self, other: &mdq::select::CodeBlockMatcher) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::CodeBlockMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::CodeBlockMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::CodeBlockMatcher::into(self) -> U
pub type mdq::select::CodeBlockMatcher::Error = core::convert::Infallible
pub fn mdq::select::CodeBlockMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::CodeBlockMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::CodeBlockMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::CodeBlockMatcher::Owned = T
pub fn mdq::select::CodeBlockMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::CodeBlockMatcher::to_owned(&self) -> T
pub fn mdq::select::CodeBlockMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::CodeBlockMatcher::borrow(&self) -> &T
pub fn mdq::select::CodeBlockMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::CodeBlockMatcher::from(t: T) -> T
pub struct mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::clone(&self) -> mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::cmp(&self, other: &mdq::select::FrontMatterMatcher) -> core::cmp::Ordering
pub fn mdq::select::FrontMatterMatcher::eq(&self, other: &mdq::select::FrontMatterMatcher) -> bool
pub fn mdq::select::FrontMatterMatcher::partial_cmp(&self, other: &mdq::select::FrontMatterMatcher) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::FrontMatterMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::FrontMatterMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::FrontMatterMatcher::into(self) -> U
pub type mdq::select::FrontMatterMatcher::Error = core::convert::Infallible
pub fn mdq::select::FrontMatterMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::FrontMatterMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::FrontMatterMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::FrontMatterMatcher::Owned = T
pub fn mdq::select::FrontMatterMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::FrontMatterMatcher::to_owned(&self) -> T
pub fn mdq::select::FrontMatterMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::FrontMatterMatcher::borrow(&self) -> &T
pub fn mdq::select::FrontMatterMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::FrontMatterMatcher::from(t: T) -> T
pub struct mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::clone(&self) -> mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::cmp(&self, other: &mdq::select::HtmlMatcher) -> core::cmp::Ordering
pub fn mdq::select::HtmlMatcher::eq(&self, other: &mdq::select::HtmlMatcher) -> bool
pub fn mdq::select::HtmlMatcher::partial_cmp(&self, other: &mdq::select::HtmlMatcher) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::HtmlMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::HtmlMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::HtmlMatcher::into(self) -> U
pub type mdq::select::HtmlMatcher::Error = core::convert::Infallible
pub fn mdq::select::HtmlMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::HtmlMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::HtmlMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::HtmlMatcher::Owned = T
pub fn mdq::select::HtmlMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::HtmlMatcher::to_owned(&self) -> T
pub fn mdq::select::HtmlMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::HtmlMatcher::borrow(&self) -> &T
pub fn mdq::select::HtmlMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::HtmlMatcher::from(t: T) -> T
pub struct mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::clone(&self) -> mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::cmp(&self, other: &mdq::select::LinklikeMatcher) -> core::cmp::Ordering
pub fn mdq::select::LinklikeMatcher::eq(&self, other: &mdq::select::LinklikeMatcher) -> bool
pub fn mdq::select::LinklikeMatcher::partial_cmp(&self, other: &mdq::select::LinklikeMatcher) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::LinklikeMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::LinklikeMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::LinklikeMatcher::into(self) -> U
pub type mdq::select::LinklikeMatcher::Error = core::convert::Infallible
pub fn mdq::select::LinklikeMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::LinklikeMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::LinklikeMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::LinklikeMatcher::Owned = T
pub fn mdq::select::LinklikeMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::LinklikeMatcher::to_owned(&self) -> T
pub fn mdq::select::LinklikeMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::LinklikeMatcher::borrow(&self) -> &T
pub fn mdq::select::LinklikeMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::LinklikeMatcher::from(t: T) -> T
pub struct mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::clone(&self) -> mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::cmp(&self, other: &mdq::select::ListItemMatcher) -> core::cmp::Ordering
pub fn mdq::select::ListItemMatcher::eq(&self, other: &mdq::select::ListItemMatcher) -> bool
pub fn mdq::select::ListItemMatcher::partial_cmp(&self, other: &mdq::select::ListItemMatcher) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::ListItemMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::ListItemMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::ListItemMatcher::into(self) -> U
pub type mdq::select::ListItemMatcher::Error = core::convert::Infallible
pub fn mdq::select::ListItemMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::ListItemMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::ListItemMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::ListItemMatcher::Owned = T
pub fn mdq::select::ListItemMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::ListItemMatcher::to_owned(&self) -> T
pub fn mdq::select::ListItemMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::ListItemMatcher::borrow(&self) -> &T
pub fn mdq::select::ListItemMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::ListItemMatcher::from(t: T) -> T
pub struct mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::clone(&self) -> mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::cmp(&self, other: &mdq::select::MatchReplace) -> core::cmp::Ordering
pub fn mdq::select::MatchReplace::eq(&self, other: &mdq::select::MatchReplace) -> bool
pub fn mdq::select::MatchReplace::partial_cmp(&self, other: &mdq::select::MatchReplace) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::MatchReplace::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::MatchReplace::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::MatchReplace::into(self) -> U
pub type mdq::select::MatchReplace::Error = core::convert::Infallible
pub fn mdq::select::MatchReplace::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::MatchReplace::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::MatchReplace::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::MatchReplace::Owned = T
pub fn mdq::select::MatchReplace::clone_into(&self, target: &mut T)
pub fn mdq::select::MatchReplace::to_owned(&self) -> T
pub fn mdq::select::MatchReplace::type_id(&self) -> core::any::TypeId
pub fn mdq::select::MatchReplace::borrow(&self) -> &T
pub fn mdq::select::MatchReplace::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::MatchReplace::from(t: T) -> T
pub struct mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::clone(&self) -> mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::cmp(&self, other: &mdq::select::ParagraphMatcher) -> core::cmp::Ordering
pub fn mdq::select::ParagraphMatcher::eq(&self, other: &mdq::select::ParagraphMatcher) -> bool
pub fn mdq::select::ParagraphMatcher::partial_cmp(&self, other: &mdq::select::ParagraphMatcher) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::ParagraphMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::ParagraphMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::ParagraphMatcher::into(self) -> U
pub type mdq::select::ParagraphMatcher::Error = core::convert::Infallible
pub fn mdq::select::ParagraphMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::ParagraphMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::ParagraphMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::ParagraphMatcher::Owned = T
pub fn mdq::select::ParagraphMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::ParagraphMatcher::to_owned(&self) -> T
pub fn mdq::select::ParagraphMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::ParagraphMatcher::borrow(&self) -> &T
pub fn mdq::select::ParagraphMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::ParagraphMatcher::from(t: T) -> T
pub struct mdq::select::ParseError
pub fn mdq::select::ParseError::to_string(&self, query_text: &str) -> alloc::string::String
pub fn mdq::select::ParseError::clone(&self) -> mdq::select::ParseError
pub fn mdq::select::ParseError::eq(&self, other: &mdq::select::ParseError) -> bool
pub fn mdq::select::ParseError::source(&self) -> core::option::Option<&(dyn core::error::Error + 'static)>
pub fn mdq::select::ParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::ParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::ParseError::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::ParseError::into(self) -> U
pub type mdq::select::ParseError::Error = core::convert::Infallible
pub fn mdq::select::ParseError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::ParseError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::ParseError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::ParseError::Owned = T
pub fn mdq::select::ParseError::clone_into(&self, target: &mut T)
pub fn mdq::select::ParseError::to_owned(&self) -> T
pub fn mdq::select::ParseError::to_string(&self) -> alloc::string::String
pub fn mdq::select::ParseError::type_id(&self) -> core::any::TypeId
pub fn mdq::select::ParseError::borrow(&self) -> &T
pub fn mdq::select::ParseError::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::ParseError::from(t: T) -> T
pub struct mdq::select::Regex
pub fn mdq::select::Regex::clone(&self) -> mdq::select::Regex
pub fn mdq::select::Regex::cmp(&self, other: &Self) -> core::cmp::Ordering
pub fn mdq::select::Regex::eq(&self, other: &Self) -> bool
pub fn mdq::select::Regex::partial_cmp(&self, other: &Self) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::Regex::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::Regex::hash<H: core::hash::Hasher>(&self, state: &mut H)
pub fn mdq::select::Regex::into(self) -> U
pub type mdq::select::Regex::Error = core::convert::Infallible
pub fn mdq::select::Regex::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::Regex::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::Regex::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::Regex::Owned = T
pub fn mdq::select::Regex::clone_into(&self, target: &mut T)
pub fn mdq::select::Regex::to_owned(&self) -> T
pub fn mdq::select::Regex::type_id(&self) -> core::any::TypeId
pub fn mdq::select::Regex::borrow(&self) -> &T
pub fn mdq::select::Regex::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::Regex::from(t: T) -> T
pub struct mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::clone(&self) -> mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::cmp(&self, other: &mdq::select::SectionMatcher) -> core::cmp::Ordering
pub fn mdq::select::SectionMatcher::eq(&self, other: &mdq::select::SectionMatcher) -> bool
pub fn mdq::select::SectionMatcher::partial_cmp(&self, other: &mdq::select::SectionMatcher) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::SectionMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::SectionMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::SectionMatcher::into(self) -> U
pub type mdq::select::SectionMatcher::Error = core::convert::Infallible
pub fn mdq::select::SectionMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::SectionMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::SectionMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::SectionMatcher::Owned = T
pub fn mdq::select::SectionMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::SectionMatcher::to_owned(&self) -> T
pub fn mdq::select::SectionMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::SectionMatcher::borrow(&self) -> &T
pub fn mdq::select::SectionMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::SectionMatcher::from(t: T) -> T
pub struct mdq::select::SelectError
pub fn mdq::select::SelectError::clone(&self) -> mdq::select::SelectError
pub fn mdq::select::SelectError::eq(&self, other: &mdq::select::SelectError) -> bool
pub fn mdq::select::SelectError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::SelectError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::SelectError::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::SelectError::into(self) -> U
pub type mdq::select::SelectError::Error = core::convert::Infallible
pub fn mdq::select::SelectError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::SelectError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::SelectError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::SelectError::Owned = T
pub fn mdq::select::SelectError::clone_into(&self, target: &mut T)
pub fn mdq::select::SelectError::to_owned(&self) -> T
pub fn mdq::select::SelectError::to_string(&self) -> alloc::string::String
pub fn mdq::select::SelectError::type_id(&self) -> core::any::TypeId
pub fn mdq::select::SelectError::borrow(&self) -> &T
pub fn mdq::select::SelectError::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::SelectError::from(t: T) -> T
pub struct mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::clone(&self) -> mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::cmp(&self, other: &mdq::select::TableMatcher) -> core::cmp::Ordering
pub fn mdq::select::TableMatcher::eq(&self, other: &mdq::select::TableMatcher) -> bool
pub fn mdq::select::TableMatcher::partial_cmp(&self, other: &mdq::select::TableMatcher) -> core::option::Option<core::cmp::Ordering>
pub fn mdq::select::TableMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::TableMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::TableMatcher::into(self) -> U
pub type mdq::select::TableMatcher::Error = core::convert::Infallible
pub fn mdq::select::TableMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub type mdq::select::TableMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::TableMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub type mdq::select::TableMatcher::Owned = T
pub fn mdq::select::TableMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::TableMatcher::to_owned(&self) -> T
pub fn mdq::select::TableMatcher::type_id(&self) -> core::any::TypeId
pub fn mdq::select::TableMatcher::borrow(&self) -> &T
pub fn mdq::select::TableMatcher::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::TableMatcher::from(t: T) -> T
pub type mdq::select::Result<T> = core::result::Result<T, mdq::select::SelectError>


