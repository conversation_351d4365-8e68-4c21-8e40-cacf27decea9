pub mod mdq
pub mod mdq::md_elem
pub mod mdq::md_elem::elem
pub enum mdq::md_elem::elem::CodeVariant
pub mdq::md_elem::elem::CodeVariant::Code(core::option::Option<CodeOpts>)
pub mdq::md_elem::elem::CodeVariant::Math
pub mdq::md_elem::elem::CodeVariant::Math::metadata: core::option::Option<alloc::string::String>
impl core::clone::Clone for mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::clone(&self) -> mdq::md_elem::elem::CodeVariant
impl core::cmp::Eq for mdq::md_elem::elem::CodeVariant
impl core::cmp::PartialEq for mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::eq(&self, other: &mdq::md_elem::elem::CodeVariant) -> bool
impl core::fmt::Debug for mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::CodeVariant
impl core::marker::Freeze for mdq::md_elem::elem::CodeVariant
impl core::marker::Send for mdq::md_elem::elem::CodeVariant
impl core::marker::Sync for mdq::md_elem::elem::CodeVariant
impl core::marker::Unpin for mdq::md_elem::elem::CodeVariant
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::CodeVariant
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::CodeVariant
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::CodeVariant where U: core::convert::From<T>
pub fn mdq::md_elem::elem::CodeVariant::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::CodeVariant where U: core::convert::Into<T>
pub type mdq::md_elem::elem::CodeVariant::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::CodeVariant::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::CodeVariant where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::CodeVariant::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::CodeVariant::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::CodeVariant where T: core::clone::Clone
pub type mdq::md_elem::elem::CodeVariant::Owned = T
pub fn mdq::md_elem::elem::CodeVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::CodeVariant::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::CodeVariant where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::CodeVariant::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::CodeVariant where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::CodeVariant::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::CodeVariant where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::CodeVariant::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::CodeVariant where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::CodeVariant::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::CodeVariant
pub fn mdq::md_elem::elem::CodeVariant::from(t: T) -> T
pub enum mdq::md_elem::elem::FrontMatterVariant
pub mdq::md_elem::elem::FrontMatterVariant::Toml
pub mdq::md_elem::elem::FrontMatterVariant::Yaml
impl mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::name(self) -> &'static str
pub fn mdq::md_elem::elem::FrontMatterVariant::separator(self) -> &'static str
impl core::clone::Clone for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::clone(&self) -> mdq::md_elem::elem::FrontMatterVariant
impl core::cmp::Eq for mdq::md_elem::elem::FrontMatterVariant
impl core::cmp::Ord for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::cmp(&self, other: &mdq::md_elem::elem::FrontMatterVariant) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::eq(&self, other: &mdq::md_elem::elem::FrontMatterVariant) -> bool
impl core::cmp::PartialOrd for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::partial_cmp(&self, other: &mdq::md_elem::elem::FrontMatterVariant) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::md_elem::elem::FrontMatterVariant
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::FrontMatterVariant
impl core::marker::Freeze for mdq::md_elem::elem::FrontMatterVariant
impl core::marker::Send for mdq::md_elem::elem::FrontMatterVariant
impl core::marker::Sync for mdq::md_elem::elem::FrontMatterVariant
impl core::marker::Unpin for mdq::md_elem::elem::FrontMatterVariant
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::FrontMatterVariant
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::FrontMatterVariant
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::FrontMatterVariant where U: core::convert::From<T>
pub fn mdq::md_elem::elem::FrontMatterVariant::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::FrontMatterVariant where U: core::convert::Into<T>
pub type mdq::md_elem::elem::FrontMatterVariant::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::FrontMatterVariant::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::FrontMatterVariant where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::FrontMatterVariant::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::FrontMatterVariant::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::FrontMatterVariant where T: core::clone::Clone
pub type mdq::md_elem::elem::FrontMatterVariant::Owned = T
pub fn mdq::md_elem::elem::FrontMatterVariant::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::FrontMatterVariant::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::FrontMatterVariant where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::FrontMatterVariant::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::FrontMatterVariant where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::FrontMatterVariant::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::FrontMatterVariant where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::FrontMatterVariant::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::FrontMatterVariant where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::FrontMatterVariant::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::FrontMatterVariant
pub fn mdq::md_elem::elem::FrontMatterVariant::from(t: T) -> T
impl<T> pest::RuleType for mdq::md_elem::elem::FrontMatterVariant where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub enum mdq::md_elem::elem::Inline
pub mdq::md_elem::elem::Inline::Footnote(FootnoteId)
pub mdq::md_elem::elem::Inline::Image(Image)
pub mdq::md_elem::elem::Inline::Link(Link)
pub mdq::md_elem::elem::Inline::Span(Span)
pub mdq::md_elem::elem::Inline::Text(Text)
impl core::clone::Clone for mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::clone(&self) -> mdq::md_elem::elem::Inline
impl core::cmp::Eq for mdq::md_elem::elem::Inline
impl core::cmp::PartialEq for mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::eq(&self, other: &mdq::md_elem::elem::Inline) -> bool
impl core::convert::From<mdq::md_elem::elem::Inline> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Inline) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::Inline
impl core::marker::Freeze for mdq::md_elem::elem::Inline
impl core::marker::Send for mdq::md_elem::elem::Inline
impl core::marker::Sync for mdq::md_elem::elem::Inline
impl core::marker::Unpin for mdq::md_elem::elem::Inline
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::Inline
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::Inline
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::Inline where U: core::convert::From<T>
pub fn mdq::md_elem::elem::Inline::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::Inline where U: core::convert::Into<T>
pub type mdq::md_elem::elem::Inline::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::Inline::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::Inline where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::Inline::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::Inline::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::Inline where T: core::clone::Clone
pub type mdq::md_elem::elem::Inline::Owned = T
pub fn mdq::md_elem::elem::Inline::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::Inline::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::Inline where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::Inline::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::Inline where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Inline::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::Inline where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::Inline::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::Inline where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::Inline::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::Inline
pub fn mdq::md_elem::elem::Inline::from(t: T) -> T
pub struct mdq::md_elem::elem::BlockHtml
pub mdq::md_elem::elem::BlockHtml::value: alloc::string::String
impl core::clone::Clone for mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::clone(&self) -> mdq::md_elem::elem::BlockHtml
impl core::cmp::Eq for mdq::md_elem::elem::BlockHtml
impl core::cmp::PartialEq for mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::eq(&self, other: &mdq::md_elem::elem::BlockHtml) -> bool
impl core::convert::From<alloc::string::String> for mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::from(value: alloc::string::String) -> Self
impl core::convert::From<mdq::md_elem::elem::BlockHtml> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockHtml) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::BlockHtml
impl core::marker::Freeze for mdq::md_elem::elem::BlockHtml
impl core::marker::Send for mdq::md_elem::elem::BlockHtml
impl core::marker::Sync for mdq::md_elem::elem::BlockHtml
impl core::marker::Unpin for mdq::md_elem::elem::BlockHtml
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::BlockHtml
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::BlockHtml
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::BlockHtml where U: core::convert::From<T>
pub fn mdq::md_elem::elem::BlockHtml::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::BlockHtml where U: core::convert::Into<T>
pub type mdq::md_elem::elem::BlockHtml::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::BlockHtml::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::BlockHtml where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::BlockHtml::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::BlockHtml::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::BlockHtml where T: core::clone::Clone
pub type mdq::md_elem::elem::BlockHtml::Owned = T
pub fn mdq::md_elem::elem::BlockHtml::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::BlockHtml::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::BlockHtml where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::BlockHtml::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::BlockHtml where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::BlockHtml::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::BlockHtml where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::BlockHtml::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::BlockHtml where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::BlockHtml::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::BlockHtml
pub fn mdq::md_elem::elem::BlockHtml::from(t: T) -> T
pub struct mdq::md_elem::elem::FrontMatter
pub mdq::md_elem::elem::FrontMatter::body: alloc::string::String
pub mdq::md_elem::elem::FrontMatter::variant: mdq::md_elem::elem::FrontMatterVariant
impl core::clone::Clone for mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::clone(&self) -> mdq::md_elem::elem::FrontMatter
impl core::cmp::Eq for mdq::md_elem::elem::FrontMatter
impl core::cmp::PartialEq for mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::eq(&self, other: &mdq::md_elem::elem::FrontMatter) -> bool
impl core::convert::From<mdq::md_elem::elem::FrontMatter> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::FrontMatter) -> Self
impl core::fmt::Debug for mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::elem::FrontMatter
impl core::marker::Freeze for mdq::md_elem::elem::FrontMatter
impl core::marker::Send for mdq::md_elem::elem::FrontMatter
impl core::marker::Sync for mdq::md_elem::elem::FrontMatter
impl core::marker::Unpin for mdq::md_elem::elem::FrontMatter
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::elem::FrontMatter
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::elem::FrontMatter
impl<T, U> core::convert::Into<U> for mdq::md_elem::elem::FrontMatter where U: core::convert::From<T>
pub fn mdq::md_elem::elem::FrontMatter::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::elem::FrontMatter where U: core::convert::Into<T>
pub type mdq::md_elem::elem::FrontMatter::Error = core::convert::Infallible
pub fn mdq::md_elem::elem::FrontMatter::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::elem::FrontMatter where U: core::convert::TryFrom<T>
pub type mdq::md_elem::elem::FrontMatter::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::elem::FrontMatter::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::elem::FrontMatter where T: core::clone::Clone
pub type mdq::md_elem::elem::FrontMatter::Owned = T
pub fn mdq::md_elem::elem::FrontMatter::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::elem::FrontMatter::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::elem::FrontMatter where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::elem::FrontMatter::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::elem::FrontMatter where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::FrontMatter::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::elem::FrontMatter where T: ?core::marker::Sized
pub fn mdq::md_elem::elem::FrontMatter::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::elem::FrontMatter where T: core::clone::Clone
pub unsafe fn mdq::md_elem::elem::FrontMatter::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::elem::FrontMatter
pub fn mdq::md_elem::elem::FrontMatter::from(t: T) -> T
pub type mdq::md_elem::elem::TableCell = alloc::vec::Vec<mdq::md_elem::elem::Inline>
pub type mdq::md_elem::elem::TableRow = alloc::vec::Vec<mdq::md_elem::elem::TableCell>
pub enum mdq::md_elem::InvalidMd
pub mdq::md_elem::InvalidMd::ConflictingReferenceDefinition(alloc::string::String)
pub mdq::md_elem::InvalidMd::InternalError(mdq::md_elem::UnknownMdParseError)
pub mdq::md_elem::InvalidMd::MissingReferenceDefinition(alloc::string::String)
pub mdq::md_elem::InvalidMd::NonInlineWhereInlineExpected(mdq::md_elem::MdElem)
pub mdq::md_elem::InvalidMd::NonListItemDirectlyUnderList(mdq::md_elem::MarkdownPart)
pub mdq::md_elem::InvalidMd::NonRowDirectlyUnderTable(mdq::md_elem::MarkdownPart)
pub mdq::md_elem::InvalidMd::ParseError(alloc::string::String)
pub mdq::md_elem::InvalidMd::UnknownMarkdown(&'static str)
pub mdq::md_elem::InvalidMd::Unsupported(mdq::md_elem::MarkdownPart)
impl core::cmp::Eq for mdq::md_elem::InvalidMd
impl core::cmp::PartialEq for mdq::md_elem::InvalidMd
pub fn mdq::md_elem::InvalidMd::eq(&self, other: &mdq::md_elem::InvalidMd) -> bool
impl core::error::Error for mdq::md_elem::InvalidMd
impl core::fmt::Debug for mdq::md_elem::InvalidMd
pub fn mdq::md_elem::InvalidMd::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::md_elem::InvalidMd
pub fn mdq::md_elem::InvalidMd::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::StructuralPartialEq for mdq::md_elem::InvalidMd
impl !core::marker::Freeze for mdq::md_elem::InvalidMd
impl core::marker::Send for mdq::md_elem::InvalidMd
impl core::marker::Sync for mdq::md_elem::InvalidMd
impl core::marker::Unpin for mdq::md_elem::InvalidMd
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::InvalidMd
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::InvalidMd
impl<T, U> core::convert::Into<U> for mdq::md_elem::InvalidMd where U: core::convert::From<T>
pub fn mdq::md_elem::InvalidMd::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::InvalidMd where U: core::convert::Into<T>
pub type mdq::md_elem::InvalidMd::Error = core::convert::Infallible
pub fn mdq::md_elem::InvalidMd::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::InvalidMd where U: core::convert::TryFrom<T>
pub type mdq::md_elem::InvalidMd::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::InvalidMd::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::string::ToString for mdq::md_elem::InvalidMd where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::md_elem::InvalidMd::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::md_elem::InvalidMd where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::InvalidMd::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::InvalidMd where T: ?core::marker::Sized
pub fn mdq::md_elem::InvalidMd::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::InvalidMd where T: ?core::marker::Sized
pub fn mdq::md_elem::InvalidMd::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::md_elem::InvalidMd
pub fn mdq::md_elem::InvalidMd::from(t: T) -> T
pub enum mdq::md_elem::MdElem
pub mdq::md_elem::MdElem::BlockHtml(mdq::md_elem::elem::BlockHtml)
pub mdq::md_elem::MdElem::BlockQuote(BlockQuote)
pub mdq::md_elem::MdElem::CodeBlock(CodeBlock)
pub mdq::md_elem::MdElem::Doc(alloc::vec::Vec<mdq::md_elem::MdElem>)
pub mdq::md_elem::MdElem::FrontMatter(mdq::md_elem::elem::FrontMatter)
pub mdq::md_elem::MdElem::Inline(mdq::md_elem::elem::Inline)
pub mdq::md_elem::MdElem::List(List)
pub mdq::md_elem::MdElem::Paragraph(Paragraph)
pub mdq::md_elem::MdElem::Section(Section)
pub mdq::md_elem::MdElem::Table(Table)
pub mdq::md_elem::MdElem::ThematicBreak
impl core::clone::Clone for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::clone(&self) -> mdq::md_elem::MdElem
impl core::cmp::Eq for mdq::md_elem::MdElem
impl core::cmp::PartialEq for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::eq(&self, other: &mdq::md_elem::MdElem) -> bool
impl core::convert::From<alloc::vec::Vec<mdq::md_elem::MdElem>> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(elems: alloc::vec::Vec<mdq::md_elem::MdElem>) -> Self
impl core::convert::From<mdq::md_elem::elem::BlockHtml> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::BlockHtml) -> Self
impl core::convert::From<mdq::md_elem::elem::FrontMatter> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::FrontMatter) -> Self
impl core::convert::From<mdq::md_elem::elem::Inline> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(value: mdq::md_elem::elem::Inline) -> Self
impl core::fmt::Debug for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::md_elem::MdElem
impl core::marker::Freeze for mdq::md_elem::MdElem
impl core::marker::Send for mdq::md_elem::MdElem
impl core::marker::Sync for mdq::md_elem::MdElem
impl core::marker::Unpin for mdq::md_elem::MdElem
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::MdElem
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::MdElem
impl<T, U> core::convert::Into<U> for mdq::md_elem::MdElem where U: core::convert::From<T>
pub fn mdq::md_elem::MdElem::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::MdElem where U: core::convert::Into<T>
pub type mdq::md_elem::MdElem::Error = core::convert::Infallible
pub fn mdq::md_elem::MdElem::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::MdElem where U: core::convert::TryFrom<T>
pub type mdq::md_elem::MdElem::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::MdElem::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::MdElem where T: core::clone::Clone
pub type mdq::md_elem::MdElem::Owned = T
pub fn mdq::md_elem::MdElem::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MdElem::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::MdElem where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::MdElem::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::MdElem where T: ?core::marker::Sized
pub fn mdq::md_elem::MdElem::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::MdElem where T: ?core::marker::Sized
pub fn mdq::md_elem::MdElem::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::MdElem where T: core::clone::Clone
pub unsafe fn mdq::md_elem::MdElem::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::MdElem
pub fn mdq::md_elem::MdElem::from(t: T) -> T
pub struct mdq::md_elem::MarkdownPart
impl core::clone::Clone for mdq::md_elem::MarkdownPart
pub fn mdq::md_elem::MarkdownPart::clone(&self) -> mdq::md_elem::MarkdownPart
impl core::cmp::Eq for mdq::md_elem::MarkdownPart
impl core::cmp::PartialEq for mdq::md_elem::MarkdownPart
pub fn mdq::md_elem::MarkdownPart::eq(&self, other: &mdq::md_elem::MarkdownPart) -> bool
impl core::fmt::Debug for mdq::md_elem::MarkdownPart
pub fn mdq::md_elem::MarkdownPart::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::StructuralPartialEq for mdq::md_elem::MarkdownPart
impl core::marker::Freeze for mdq::md_elem::MarkdownPart
impl core::marker::Send for mdq::md_elem::MarkdownPart
impl core::marker::Sync for mdq::md_elem::MarkdownPart
impl core::marker::Unpin for mdq::md_elem::MarkdownPart
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::MarkdownPart
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::MarkdownPart
impl<T, U> core::convert::Into<U> for mdq::md_elem::MarkdownPart where U: core::convert::From<T>
pub fn mdq::md_elem::MarkdownPart::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::MarkdownPart where U: core::convert::Into<T>
pub type mdq::md_elem::MarkdownPart::Error = core::convert::Infallible
pub fn mdq::md_elem::MarkdownPart::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::MarkdownPart where U: core::convert::TryFrom<T>
pub type mdq::md_elem::MarkdownPart::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::MarkdownPart::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::MarkdownPart where T: core::clone::Clone
pub type mdq::md_elem::MarkdownPart::Owned = T
pub fn mdq::md_elem::MarkdownPart::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MarkdownPart::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::MarkdownPart where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::MarkdownPart::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::MarkdownPart where T: ?core::marker::Sized
pub fn mdq::md_elem::MarkdownPart::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::MarkdownPart where T: ?core::marker::Sized
pub fn mdq::md_elem::MarkdownPart::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::MarkdownPart where T: core::clone::Clone
pub unsafe fn mdq::md_elem::MarkdownPart::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::MarkdownPart
pub fn mdq::md_elem::MarkdownPart::from(t: T) -> T
pub struct mdq::md_elem::MdDoc
pub mdq::md_elem::MdDoc::ctx: MdContext
pub mdq::md_elem::MdDoc::roots: alloc::vec::Vec<mdq::md_elem::MdElem>
impl mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::parse(text: &str, options: &mdq::md_elem::ParseOptions) -> core::result::Result<Self, mdq::md_elem::InvalidMd>
impl core::clone::Clone for mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::clone(&self) -> mdq::md_elem::MdDoc
impl core::cmp::PartialEq for mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::eq(&self, other: &mdq::md_elem::MdDoc) -> bool
impl core::default::Default for mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::default() -> mdq::md_elem::MdDoc
impl core::fmt::Debug for mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::StructuralPartialEq for mdq::md_elem::MdDoc
impl core::marker::Freeze for mdq::md_elem::MdDoc
impl core::marker::Send for mdq::md_elem::MdDoc
impl core::marker::Sync for mdq::md_elem::MdDoc
impl core::marker::Unpin for mdq::md_elem::MdDoc
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::MdDoc
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::MdDoc
impl<T, U> core::convert::Into<U> for mdq::md_elem::MdDoc where U: core::convert::From<T>
pub fn mdq::md_elem::MdDoc::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::MdDoc where U: core::convert::Into<T>
pub type mdq::md_elem::MdDoc::Error = core::convert::Infallible
pub fn mdq::md_elem::MdDoc::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::MdDoc where U: core::convert::TryFrom<T>
pub type mdq::md_elem::MdDoc::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::MdDoc::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::md_elem::MdDoc where T: core::clone::Clone
pub type mdq::md_elem::MdDoc::Owned = T
pub fn mdq::md_elem::MdDoc::clone_into(&self, target: &mut T)
pub fn mdq::md_elem::MdDoc::to_owned(&self) -> T
impl<T> core::any::Any for mdq::md_elem::MdDoc where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::MdDoc::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::MdDoc where T: ?core::marker::Sized
pub fn mdq::md_elem::MdDoc::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::MdDoc where T: ?core::marker::Sized
pub fn mdq::md_elem::MdDoc::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::md_elem::MdDoc where T: core::clone::Clone
pub unsafe fn mdq::md_elem::MdDoc::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::md_elem::MdDoc
pub fn mdq::md_elem::MdDoc::from(t: T) -> T
pub struct mdq::md_elem::ParseOptions
pub mdq::md_elem::ParseOptions::allow_unknown_markdown: bool
pub mdq::md_elem::ParseOptions::mdast_options: markdown::configuration::ParseOptions
impl mdq::md_elem::ParseOptions
pub fn mdq::md_elem::ParseOptions::gfm() -> Self
impl core::default::Default for mdq::md_elem::ParseOptions
pub fn mdq::md_elem::ParseOptions::default() -> Self
impl core::fmt::Debug for mdq::md_elem::ParseOptions
pub fn mdq::md_elem::ParseOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::Freeze for mdq::md_elem::ParseOptions
impl !core::marker::Send for mdq::md_elem::ParseOptions
impl !core::marker::Sync for mdq::md_elem::ParseOptions
impl core::marker::Unpin for mdq::md_elem::ParseOptions
impl !core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::ParseOptions
impl !core::panic::unwind_safe::UnwindSafe for mdq::md_elem::ParseOptions
impl<T, U> core::convert::Into<U> for mdq::md_elem::ParseOptions where U: core::convert::From<T>
pub fn mdq::md_elem::ParseOptions::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::ParseOptions where U: core::convert::Into<T>
pub type mdq::md_elem::ParseOptions::Error = core::convert::Infallible
pub fn mdq::md_elem::ParseOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::ParseOptions where U: core::convert::TryFrom<T>
pub type mdq::md_elem::ParseOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::ParseOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> core::any::Any for mdq::md_elem::ParseOptions where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::ParseOptions::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::ParseOptions where T: ?core::marker::Sized
pub fn mdq::md_elem::ParseOptions::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::ParseOptions where T: ?core::marker::Sized
pub fn mdq::md_elem::ParseOptions::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::md_elem::ParseOptions
pub fn mdq::md_elem::ParseOptions::from(t: T) -> T
pub struct mdq::md_elem::UnknownMdParseError
impl core::cmp::Eq for mdq::md_elem::UnknownMdParseError
impl core::cmp::PartialEq for mdq::md_elem::UnknownMdParseError
pub fn mdq::md_elem::UnknownMdParseError::eq(&self, _other: &Self) -> bool
impl core::error::Error for mdq::md_elem::UnknownMdParseError
impl core::fmt::Debug for mdq::md_elem::UnknownMdParseError
pub fn mdq::md_elem::UnknownMdParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::md_elem::UnknownMdParseError
pub fn mdq::md_elem::UnknownMdParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl !core::marker::Freeze for mdq::md_elem::UnknownMdParseError
impl core::marker::Send for mdq::md_elem::UnknownMdParseError
impl core::marker::Sync for mdq::md_elem::UnknownMdParseError
impl core::marker::Unpin for mdq::md_elem::UnknownMdParseError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::md_elem::UnknownMdParseError
impl core::panic::unwind_safe::UnwindSafe for mdq::md_elem::UnknownMdParseError
impl<T, U> core::convert::Into<U> for mdq::md_elem::UnknownMdParseError where U: core::convert::From<T>
pub fn mdq::md_elem::UnknownMdParseError::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::md_elem::UnknownMdParseError where U: core::convert::Into<T>
pub type mdq::md_elem::UnknownMdParseError::Error = core::convert::Infallible
pub fn mdq::md_elem::UnknownMdParseError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::md_elem::UnknownMdParseError where U: core::convert::TryFrom<T>
pub type mdq::md_elem::UnknownMdParseError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::md_elem::UnknownMdParseError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::string::ToString for mdq::md_elem::UnknownMdParseError where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::md_elem::UnknownMdParseError::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::md_elem::UnknownMdParseError where T: 'static + ?core::marker::Sized
pub fn mdq::md_elem::UnknownMdParseError::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::md_elem::UnknownMdParseError where T: ?core::marker::Sized
pub fn mdq::md_elem::UnknownMdParseError::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::md_elem::UnknownMdParseError where T: ?core::marker::Sized
pub fn mdq::md_elem::UnknownMdParseError::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::md_elem::UnknownMdParseError
pub fn mdq::md_elem::UnknownMdParseError::from(t: T) -> T
pub mod mdq::output
#[non_exhaustive] pub enum mdq::output::MdWriterOptionsBuilderError
pub mdq::output::MdWriterOptionsBuilderError::UninitializedField(&'static str)
pub mdq::output::MdWriterOptionsBuilderError::ValidationError(alloc::string::String)
impl core::convert::From<alloc::string::String> for mdq::output::MdWriterOptionsBuilderError
pub fn mdq::output::MdWriterOptionsBuilderError::from(s: alloc::string::String) -> Self
impl core::convert::From<derive_builder::error::UninitializedFieldError> for mdq::output::MdWriterOptionsBuilderError
pub fn mdq::output::MdWriterOptionsBuilderError::from(s: derive_builder::error::UninitializedFieldError) -> Self
impl core::error::Error for mdq::output::MdWriterOptionsBuilderError
impl core::fmt::Debug for mdq::output::MdWriterOptionsBuilderError
pub fn mdq::output::MdWriterOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::output::MdWriterOptionsBuilderError
pub fn mdq::output::MdWriterOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::Freeze for mdq::output::MdWriterOptionsBuilderError
impl core::marker::Send for mdq::output::MdWriterOptionsBuilderError
impl core::marker::Sync for mdq::output::MdWriterOptionsBuilderError
impl core::marker::Unpin for mdq::output::MdWriterOptionsBuilderError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::MdWriterOptionsBuilderError
impl core::panic::unwind_safe::UnwindSafe for mdq::output::MdWriterOptionsBuilderError
impl<T, U> core::convert::Into<U> for mdq::output::MdWriterOptionsBuilderError where U: core::convert::From<T>
pub fn mdq::output::MdWriterOptionsBuilderError::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::MdWriterOptionsBuilderError where U: core::convert::Into<T>
pub type mdq::output::MdWriterOptionsBuilderError::Error = core::convert::Infallible
pub fn mdq::output::MdWriterOptionsBuilderError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::MdWriterOptionsBuilderError where U: core::convert::TryFrom<T>
pub type mdq::output::MdWriterOptionsBuilderError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::MdWriterOptionsBuilderError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::string::ToString for mdq::output::MdWriterOptionsBuilderError where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilderError::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::output::MdWriterOptionsBuilderError where T: 'static + ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilderError::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::MdWriterOptionsBuilderError where T: ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilderError::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::MdWriterOptionsBuilderError where T: ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilderError::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::output::MdWriterOptionsBuilderError
pub fn mdq::output::MdWriterOptionsBuilderError::from(t: T) -> T
pub enum mdq::output::ReferencePlacement
pub mdq::output::ReferencePlacement::Doc
pub mdq::output::ReferencePlacement::Section
impl clap_builder::derive::ValueEnum for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::to_possible_value<'a>(&self) -> core::option::Option<clap_builder::builder::possible_value::PossibleValue>
pub fn mdq::output::ReferencePlacement::value_variants<'a>() -> &'a [Self]
impl core::clone::Clone for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::clone(&self) -> mdq::output::ReferencePlacement
impl core::cmp::Eq for mdq::output::ReferencePlacement
impl core::cmp::Ord for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::cmp(&self, other: &mdq::output::ReferencePlacement) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::eq(&self, other: &mdq::output::ReferencePlacement) -> bool
impl core::cmp::PartialOrd for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::partial_cmp(&self, other: &mdq::output::ReferencePlacement) -> core::option::Option<core::cmp::Ordering>
impl core::default::Default for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::default() -> mdq::output::ReferencePlacement
impl core::fmt::Debug for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::output::ReferencePlacement
impl core::marker::StructuralPartialEq for mdq::output::ReferencePlacement
impl core::marker::Freeze for mdq::output::ReferencePlacement
impl core::marker::Send for mdq::output::ReferencePlacement
impl core::marker::Sync for mdq::output::ReferencePlacement
impl core::marker::Unpin for mdq::output::ReferencePlacement
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::ReferencePlacement
impl core::panic::unwind_safe::UnwindSafe for mdq::output::ReferencePlacement
impl<T, U> core::convert::Into<U> for mdq::output::ReferencePlacement where U: core::convert::From<T>
pub fn mdq::output::ReferencePlacement::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::ReferencePlacement where U: core::convert::Into<T>
pub type mdq::output::ReferencePlacement::Error = core::convert::Infallible
pub fn mdq::output::ReferencePlacement::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::ReferencePlacement where U: core::convert::TryFrom<T>
pub type mdq::output::ReferencePlacement::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::ReferencePlacement::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::ReferencePlacement where T: core::clone::Clone
pub type mdq::output::ReferencePlacement::Owned = T
pub fn mdq::output::ReferencePlacement::clone_into(&self, target: &mut T)
pub fn mdq::output::ReferencePlacement::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::ReferencePlacement where T: 'static + ?core::marker::Sized
pub fn mdq::output::ReferencePlacement::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::ReferencePlacement where T: ?core::marker::Sized
pub fn mdq::output::ReferencePlacement::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::ReferencePlacement where T: ?core::marker::Sized
pub fn mdq::output::ReferencePlacement::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::ReferencePlacement where T: core::clone::Clone
pub unsafe fn mdq::output::ReferencePlacement::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::ReferencePlacement
pub fn mdq::output::ReferencePlacement::from(t: T) -> T
impl<T> pest::RuleType for mdq::output::ReferencePlacement where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub struct mdq::output::MdWriter
impl mdq::output::MdWriter
pub fn mdq::output::MdWriter::with_options(options: mdq::output::MdWriterOptions) -> Self
pub fn mdq::output::MdWriter::write<'md, I, W>(&self, ctx: &'md MdContext, nodes: I, out: &mut W) where I: core::iter::traits::collect::IntoIterator<Item = &'md mdq::md_elem::MdElem>, W: core::fmt::Write
impl core::clone::Clone for mdq::output::MdWriter
pub fn mdq::output::MdWriter::clone(&self) -> mdq::output::MdWriter
impl core::cmp::Eq for mdq::output::MdWriter
impl core::cmp::Ord for mdq::output::MdWriter
pub fn mdq::output::MdWriter::cmp(&self, other: &mdq::output::MdWriter) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::output::MdWriter
pub fn mdq::output::MdWriter::eq(&self, other: &mdq::output::MdWriter) -> bool
impl core::cmp::PartialOrd for mdq::output::MdWriter
pub fn mdq::output::MdWriter::partial_cmp(&self, other: &mdq::output::MdWriter) -> core::option::Option<core::cmp::Ordering>
impl core::default::Default for mdq::output::MdWriter
pub fn mdq::output::MdWriter::default() -> mdq::output::MdWriter
impl core::fmt::Debug for mdq::output::MdWriter
pub fn mdq::output::MdWriter::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::output::MdWriter
pub fn mdq::output::MdWriter::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::output::MdWriter
impl core::marker::StructuralPartialEq for mdq::output::MdWriter
impl core::marker::Freeze for mdq::output::MdWriter
impl core::marker::Send for mdq::output::MdWriter
impl core::marker::Sync for mdq::output::MdWriter
impl core::marker::Unpin for mdq::output::MdWriter
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::MdWriter
impl core::panic::unwind_safe::UnwindSafe for mdq::output::MdWriter
impl<T, U> core::convert::Into<U> for mdq::output::MdWriter where U: core::convert::From<T>
pub fn mdq::output::MdWriter::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::MdWriter where U: core::convert::Into<T>
pub type mdq::output::MdWriter::Error = core::convert::Infallible
pub fn mdq::output::MdWriter::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::MdWriter where U: core::convert::TryFrom<T>
pub type mdq::output::MdWriter::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::MdWriter::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::MdWriter where T: core::clone::Clone
pub type mdq::output::MdWriter::Owned = T
pub fn mdq::output::MdWriter::clone_into(&self, target: &mut T)
pub fn mdq::output::MdWriter::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::MdWriter where T: 'static + ?core::marker::Sized
pub fn mdq::output::MdWriter::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::MdWriter where T: ?core::marker::Sized
pub fn mdq::output::MdWriter::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::MdWriter where T: ?core::marker::Sized
pub fn mdq::output::MdWriter::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::MdWriter where T: core::clone::Clone
pub unsafe fn mdq::output::MdWriter::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::MdWriter
pub fn mdq::output::MdWriter::from(t: T) -> T
impl<T> pest::RuleType for mdq::output::MdWriter where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub struct mdq::output::MdWriterOptions
pub mdq::output::MdWriterOptions::footnote_reference_placement: mdq::output::ReferencePlacement
pub mdq::output::MdWriterOptions::include_thematic_breaks: bool
pub mdq::output::MdWriterOptions::inline_options: crate::output::fmt_md_inlines::InlineElemOptions
pub mdq::output::MdWriterOptions::link_reference_placement: mdq::output::ReferencePlacement
pub mdq::output::MdWriterOptions::text_width: core::option::Option<usize>
impl core::clone::Clone for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::clone(&self) -> mdq::output::MdWriterOptions
impl core::cmp::Eq for mdq::output::MdWriterOptions
impl core::cmp::Ord for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::cmp(&self, other: &mdq::output::MdWriterOptions) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::eq(&self, other: &mdq::output::MdWriterOptions) -> bool
impl core::cmp::PartialOrd for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::partial_cmp(&self, other: &mdq::output::MdWriterOptions) -> core::option::Option<core::cmp::Ordering>
impl core::convert::From<&mdq::run::RunOptions> for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::from(cli: &mdq::run::RunOptions) -> Self
impl core::default::Default for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::default() -> mdq::output::MdWriterOptions
impl core::fmt::Debug for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::output::MdWriterOptions
impl core::marker::StructuralPartialEq for mdq::output::MdWriterOptions
impl core::marker::Freeze for mdq::output::MdWriterOptions
impl core::marker::Send for mdq::output::MdWriterOptions
impl core::marker::Sync for mdq::output::MdWriterOptions
impl core::marker::Unpin for mdq::output::MdWriterOptions
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::MdWriterOptions
impl core::panic::unwind_safe::UnwindSafe for mdq::output::MdWriterOptions
impl<T, U> core::convert::Into<U> for mdq::output::MdWriterOptions where U: core::convert::From<T>
pub fn mdq::output::MdWriterOptions::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::MdWriterOptions where U: core::convert::Into<T>
pub type mdq::output::MdWriterOptions::Error = core::convert::Infallible
pub fn mdq::output::MdWriterOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::MdWriterOptions where U: core::convert::TryFrom<T>
pub type mdq::output::MdWriterOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::MdWriterOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::MdWriterOptions where T: core::clone::Clone
pub type mdq::output::MdWriterOptions::Owned = T
pub fn mdq::output::MdWriterOptions::clone_into(&self, target: &mut T)
pub fn mdq::output::MdWriterOptions::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::MdWriterOptions where T: 'static + ?core::marker::Sized
pub fn mdq::output::MdWriterOptions::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::MdWriterOptions where T: ?core::marker::Sized
pub fn mdq::output::MdWriterOptions::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::MdWriterOptions where T: ?core::marker::Sized
pub fn mdq::output::MdWriterOptions::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::MdWriterOptions where T: core::clone::Clone
pub unsafe fn mdq::output::MdWriterOptions::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::from(t: T) -> T
impl<T> pest::RuleType for mdq::output::MdWriterOptions where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub struct mdq::output::MdWriterOptionsBuilder
impl mdq::output::MdWriterOptionsBuilder
pub fn mdq::output::MdWriterOptionsBuilder::build(&self) -> core::result::Result<mdq::output::MdWriterOptions, mdq::output::MdWriterOptionsBuilderError>
pub fn mdq::output::MdWriterOptionsBuilder::footnote_reference_placement(&mut self, value: mdq::output::ReferencePlacement) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::include_thematic_breaks(&mut self, value: bool) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::inline_options(&mut self, value: InlineElemOptions) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::link_reference_placement(&mut self, value: mdq::output::ReferencePlacement) -> &mut Self
pub fn mdq::output::MdWriterOptionsBuilder::text_width(&mut self, value: core::option::Option<usize>) -> &mut Self
impl core::clone::Clone for mdq::output::MdWriterOptionsBuilder
pub fn mdq::output::MdWriterOptionsBuilder::clone(&self) -> mdq::output::MdWriterOptionsBuilder
impl core::default::Default for mdq::output::MdWriterOptionsBuilder
pub fn mdq::output::MdWriterOptionsBuilder::default() -> Self
impl core::marker::Freeze for mdq::output::MdWriterOptionsBuilder
impl core::marker::Send for mdq::output::MdWriterOptionsBuilder
impl core::marker::Sync for mdq::output::MdWriterOptionsBuilder
impl core::marker::Unpin for mdq::output::MdWriterOptionsBuilder
impl core::panic::unwind_safe::RefUnwindSafe for mdq::output::MdWriterOptionsBuilder
impl core::panic::unwind_safe::UnwindSafe for mdq::output::MdWriterOptionsBuilder
impl<T, U> core::convert::Into<U> for mdq::output::MdWriterOptionsBuilder where U: core::convert::From<T>
pub fn mdq::output::MdWriterOptionsBuilder::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::output::MdWriterOptionsBuilder where U: core::convert::Into<T>
pub type mdq::output::MdWriterOptionsBuilder::Error = core::convert::Infallible
pub fn mdq::output::MdWriterOptionsBuilder::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::output::MdWriterOptionsBuilder where U: core::convert::TryFrom<T>
pub type mdq::output::MdWriterOptionsBuilder::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::output::MdWriterOptionsBuilder::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::output::MdWriterOptionsBuilder where T: core::clone::Clone
pub type mdq::output::MdWriterOptionsBuilder::Owned = T
pub fn mdq::output::MdWriterOptionsBuilder::clone_into(&self, target: &mut T)
pub fn mdq::output::MdWriterOptionsBuilder::to_owned(&self) -> T
impl<T> core::any::Any for mdq::output::MdWriterOptionsBuilder where T: 'static + ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilder::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::output::MdWriterOptionsBuilder where T: ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilder::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::output::MdWriterOptionsBuilder where T: ?core::marker::Sized
pub fn mdq::output::MdWriterOptionsBuilder::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::output::MdWriterOptionsBuilder where T: core::clone::Clone
pub unsafe fn mdq::output::MdWriterOptionsBuilder::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::output::MdWriterOptionsBuilder
pub fn mdq::output::MdWriterOptionsBuilder::from(t: T) -> T
pub mod mdq::run
pub enum mdq::run::OutputFormat
pub mdq::run::OutputFormat::Json
pub mdq::run::OutputFormat::Markdown
pub mdq::run::OutputFormat::Md
pub mdq::run::OutputFormat::Plain
impl clap_builder::derive::ValueEnum for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::to_possible_value<'a>(&self) -> core::option::Option<clap_builder::builder::possible_value::PossibleValue>
pub fn mdq::run::OutputFormat::value_variants<'a>() -> &'a [Self]
impl core::clone::Clone for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::clone(&self) -> mdq::run::OutputFormat
impl core::cmp::Eq for mdq::run::OutputFormat
impl core::cmp::Ord for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::cmp(&self, other: &mdq::run::OutputFormat) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::eq(&self, other: &mdq::run::OutputFormat) -> bool
impl core::cmp::PartialOrd for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::partial_cmp(&self, other: &mdq::run::OutputFormat) -> core::option::Option<core::cmp::Ordering>
impl core::default::Default for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::default() -> Self
impl core::fmt::Debug for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::run::OutputFormat
impl core::marker::StructuralPartialEq for mdq::run::OutputFormat
impl core::marker::Freeze for mdq::run::OutputFormat
impl core::marker::Send for mdq::run::OutputFormat
impl core::marker::Sync for mdq::run::OutputFormat
impl core::marker::Unpin for mdq::run::OutputFormat
impl core::panic::unwind_safe::RefUnwindSafe for mdq::run::OutputFormat
impl core::panic::unwind_safe::UnwindSafe for mdq::run::OutputFormat
impl<T, U> core::convert::Into<U> for mdq::run::OutputFormat where U: core::convert::From<T>
pub fn mdq::run::OutputFormat::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::run::OutputFormat where U: core::convert::Into<T>
pub type mdq::run::OutputFormat::Error = core::convert::Infallible
pub fn mdq::run::OutputFormat::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::run::OutputFormat where U: core::convert::TryFrom<T>
pub type mdq::run::OutputFormat::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::OutputFormat::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::run::OutputFormat where T: core::clone::Clone
pub type mdq::run::OutputFormat::Owned = T
pub fn mdq::run::OutputFormat::clone_into(&self, target: &mut T)
pub fn mdq::run::OutputFormat::to_owned(&self) -> T
impl<T> alloc::string::ToString for mdq::run::OutputFormat where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::run::OutputFormat::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::run::OutputFormat where T: 'static + ?core::marker::Sized
pub fn mdq::run::OutputFormat::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::run::OutputFormat where T: ?core::marker::Sized
pub fn mdq::run::OutputFormat::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::run::OutputFormat where T: ?core::marker::Sized
pub fn mdq::run::OutputFormat::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::run::OutputFormat where T: core::clone::Clone
pub unsafe fn mdq::run::OutputFormat::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::run::OutputFormat
pub fn mdq::run::OutputFormat::from(t: T) -> T
impl<T> pest::RuleType for mdq::run::OutputFormat where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
#[non_exhaustive] pub enum mdq::run::RunOptionsBuilderError
pub mdq::run::RunOptionsBuilderError::UninitializedField(&'static str)
pub mdq::run::RunOptionsBuilderError::ValidationError(alloc::string::String)
impl core::convert::From<alloc::string::String> for mdq::run::RunOptionsBuilderError
pub fn mdq::run::RunOptionsBuilderError::from(s: alloc::string::String) -> Self
impl core::convert::From<derive_builder::error::UninitializedFieldError> for mdq::run::RunOptionsBuilderError
pub fn mdq::run::RunOptionsBuilderError::from(s: derive_builder::error::UninitializedFieldError) -> Self
impl core::error::Error for mdq::run::RunOptionsBuilderError
impl core::fmt::Debug for mdq::run::RunOptionsBuilderError
pub fn mdq::run::RunOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::run::RunOptionsBuilderError
pub fn mdq::run::RunOptionsBuilderError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::Freeze for mdq::run::RunOptionsBuilderError
impl core::marker::Send for mdq::run::RunOptionsBuilderError
impl core::marker::Sync for mdq::run::RunOptionsBuilderError
impl core::marker::Unpin for mdq::run::RunOptionsBuilderError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::run::RunOptionsBuilderError
impl core::panic::unwind_safe::UnwindSafe for mdq::run::RunOptionsBuilderError
impl<T, U> core::convert::Into<U> for mdq::run::RunOptionsBuilderError where U: core::convert::From<T>
pub fn mdq::run::RunOptionsBuilderError::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::run::RunOptionsBuilderError where U: core::convert::Into<T>
pub type mdq::run::RunOptionsBuilderError::Error = core::convert::Infallible
pub fn mdq::run::RunOptionsBuilderError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::run::RunOptionsBuilderError where U: core::convert::TryFrom<T>
pub type mdq::run::RunOptionsBuilderError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::RunOptionsBuilderError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::string::ToString for mdq::run::RunOptionsBuilderError where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilderError::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::run::RunOptionsBuilderError where T: 'static + ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilderError::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::run::RunOptionsBuilderError where T: ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilderError::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::run::RunOptionsBuilderError where T: ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilderError::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::run::RunOptionsBuilderError
pub fn mdq::run::RunOptionsBuilderError::from(t: T) -> T
pub struct mdq::run::RunOptions
pub mdq::run::RunOptions::add_breaks: core::option::Option<bool>
pub mdq::run::RunOptions::allow_unknown_markdown: bool
pub mdq::run::RunOptions::footnote_pos: core::option::Option<mdq::output::ReferencePlacement>
pub mdq::run::RunOptions::link_format: crate::output::LinkTransform
pub mdq::run::RunOptions::link_pos: mdq::output::ReferencePlacement
pub mdq::run::RunOptions::markdown_file_paths: alloc::vec::Vec<alloc::string::String>
pub mdq::run::RunOptions::output: mdq::run::OutputFormat
pub mdq::run::RunOptions::quiet: bool
pub mdq::run::RunOptions::renumber_footnotes: bool
pub mdq::run::RunOptions::selectors: alloc::string::String
pub mdq::run::RunOptions::wrap_width: core::option::Option<usize>
impl mdq::run::RunOptions
pub fn mdq::run::RunOptions::should_add_breaks(&self) -> bool
impl core::clone::Clone for mdq::run::RunOptions
pub fn mdq::run::RunOptions::clone(&self) -> mdq::run::RunOptions
impl core::cmp::Eq for mdq::run::RunOptions
impl core::cmp::Ord for mdq::run::RunOptions
pub fn mdq::run::RunOptions::cmp(&self, other: &mdq::run::RunOptions) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::run::RunOptions
pub fn mdq::run::RunOptions::eq(&self, other: &mdq::run::RunOptions) -> bool
impl core::cmp::PartialOrd for mdq::run::RunOptions
pub fn mdq::run::RunOptions::partial_cmp(&self, other: &mdq::run::RunOptions) -> core::option::Option<core::cmp::Ordering>
impl core::convert::From<&mdq::run::RunOptions> for mdq::output::MdWriterOptions
pub fn mdq::output::MdWriterOptions::from(cli: &mdq::run::RunOptions) -> Self
impl core::default::Default for mdq::run::RunOptions
pub fn mdq::run::RunOptions::default() -> Self
impl core::fmt::Debug for mdq::run::RunOptions
pub fn mdq::run::RunOptions::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::run::RunOptions
pub fn mdq::run::RunOptions::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::run::RunOptions
impl core::marker::Freeze for mdq::run::RunOptions
impl core::marker::Send for mdq::run::RunOptions
impl core::marker::Sync for mdq::run::RunOptions
impl core::marker::Unpin for mdq::run::RunOptions
impl core::panic::unwind_safe::RefUnwindSafe for mdq::run::RunOptions
impl core::panic::unwind_safe::UnwindSafe for mdq::run::RunOptions
impl<T, U> core::convert::Into<U> for mdq::run::RunOptions where U: core::convert::From<T>
pub fn mdq::run::RunOptions::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::run::RunOptions where U: core::convert::Into<T>
pub type mdq::run::RunOptions::Error = core::convert::Infallible
pub fn mdq::run::RunOptions::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::run::RunOptions where U: core::convert::TryFrom<T>
pub type mdq::run::RunOptions::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::RunOptions::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::run::RunOptions where T: core::clone::Clone
pub type mdq::run::RunOptions::Owned = T
pub fn mdq::run::RunOptions::clone_into(&self, target: &mut T)
pub fn mdq::run::RunOptions::to_owned(&self) -> T
impl<T> core::any::Any for mdq::run::RunOptions where T: 'static + ?core::marker::Sized
pub fn mdq::run::RunOptions::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::run::RunOptions where T: ?core::marker::Sized
pub fn mdq::run::RunOptions::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::run::RunOptions where T: ?core::marker::Sized
pub fn mdq::run::RunOptions::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::run::RunOptions where T: core::clone::Clone
pub unsafe fn mdq::run::RunOptions::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::run::RunOptions
pub fn mdq::run::RunOptions::from(t: T) -> T
pub struct mdq::run::RunOptionsBuilder
impl mdq::run::RunOptionsBuilder
pub fn mdq::run::RunOptionsBuilder::add_breaks(&mut self, value: core::option::Option<bool>) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::allow_unknown_markdown(&mut self, value: bool) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::build(&self) -> core::result::Result<mdq::run::RunOptions, mdq::run::RunOptionsBuilderError>
pub fn mdq::run::RunOptionsBuilder::footnote_pos(&mut self, value: core::option::Option<mdq::output::ReferencePlacement>) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::link_format(&mut self, value: LinkTransform) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::link_pos(&mut self, value: mdq::output::ReferencePlacement) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::markdown_file_paths(&mut self, value: alloc::vec::Vec<alloc::string::String>) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::output(&mut self, value: mdq::run::OutputFormat) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::quiet(&mut self, value: bool) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::renumber_footnotes(&mut self, value: bool) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::selectors(&mut self, value: alloc::string::String) -> &mut Self
pub fn mdq::run::RunOptionsBuilder::wrap_width(&mut self, value: core::option::Option<usize>) -> &mut Self
impl core::clone::Clone for mdq::run::RunOptionsBuilder
pub fn mdq::run::RunOptionsBuilder::clone(&self) -> mdq::run::RunOptionsBuilder
impl core::default::Default for mdq::run::RunOptionsBuilder
pub fn mdq::run::RunOptionsBuilder::default() -> Self
impl core::marker::Freeze for mdq::run::RunOptionsBuilder
impl core::marker::Send for mdq::run::RunOptionsBuilder
impl core::marker::Sync for mdq::run::RunOptionsBuilder
impl core::marker::Unpin for mdq::run::RunOptionsBuilder
impl core::panic::unwind_safe::RefUnwindSafe for mdq::run::RunOptionsBuilder
impl core::panic::unwind_safe::UnwindSafe for mdq::run::RunOptionsBuilder
impl<T, U> core::convert::Into<U> for mdq::run::RunOptionsBuilder where U: core::convert::From<T>
pub fn mdq::run::RunOptionsBuilder::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::run::RunOptionsBuilder where U: core::convert::Into<T>
pub type mdq::run::RunOptionsBuilder::Error = core::convert::Infallible
pub fn mdq::run::RunOptionsBuilder::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::run::RunOptionsBuilder where U: core::convert::TryFrom<T>
pub type mdq::run::RunOptionsBuilder::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::run::RunOptionsBuilder::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::run::RunOptionsBuilder where T: core::clone::Clone
pub type mdq::run::RunOptionsBuilder::Owned = T
pub fn mdq::run::RunOptionsBuilder::clone_into(&self, target: &mut T)
pub fn mdq::run::RunOptionsBuilder::to_owned(&self) -> T
impl<T> core::any::Any for mdq::run::RunOptionsBuilder where T: 'static + ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilder::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::run::RunOptionsBuilder where T: ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilder::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::run::RunOptionsBuilder where T: ?core::marker::Sized
pub fn mdq::run::RunOptionsBuilder::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::run::RunOptionsBuilder where T: core::clone::Clone
pub unsafe fn mdq::run::RunOptionsBuilder::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::run::RunOptionsBuilder
pub fn mdq::run::RunOptionsBuilder::from(t: T) -> T
pub trait mdq::run::OsFacade
pub fn mdq::run::OsFacade::read_all(&self, markdown_file_paths: &[alloc::string::String]) -> core::result::Result<alloc::string::String, Error>
pub fn mdq::run::OsFacade::read_file(&self, path: &str) -> std::io::error::Result<alloc::string::String>
pub fn mdq::run::OsFacade::read_stdin(&self) -> std::io::error::Result<alloc::string::String>
pub fn mdq::run::OsFacade::stdout(&mut self) -> impl std::io::Write
pub fn mdq::run::OsFacade::write_error(&mut self, err: Error)
pub fn mdq::run::run(cli: &mdq::run::RunOptions, os: &mut impl mdq::run::OsFacade) -> bool
pub mod mdq::select
pub enum mdq::select::ListItemTask
pub mdq::select::ListItemTask::Either
pub mdq::select::ListItemTask::None
pub mdq::select::ListItemTask::Selected
pub mdq::select::ListItemTask::Unselected
impl core::clone::Clone for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::clone(&self) -> mdq::select::ListItemTask
impl core::cmp::Eq for mdq::select::ListItemTask
impl core::cmp::Ord for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::cmp(&self, other: &mdq::select::ListItemTask) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::eq(&self, other: &mdq::select::ListItemTask) -> bool
impl core::cmp::PartialOrd for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::partial_cmp(&self, other: &mdq::select::ListItemTask) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::Copy for mdq::select::ListItemTask
impl core::marker::StructuralPartialEq for mdq::select::ListItemTask
impl core::marker::Freeze for mdq::select::ListItemTask
impl core::marker::Send for mdq::select::ListItemTask
impl core::marker::Sync for mdq::select::ListItemTask
impl core::marker::Unpin for mdq::select::ListItemTask
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::ListItemTask
impl core::panic::unwind_safe::UnwindSafe for mdq::select::ListItemTask
impl<T, U> core::convert::Into<U> for mdq::select::ListItemTask where U: core::convert::From<T>
pub fn mdq::select::ListItemTask::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::ListItemTask where U: core::convert::Into<T>
pub type mdq::select::ListItemTask::Error = core::convert::Infallible
pub fn mdq::select::ListItemTask::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::ListItemTask where U: core::convert::TryFrom<T>
pub type mdq::select::ListItemTask::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::ListItemTask::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::ListItemTask where T: core::clone::Clone
pub type mdq::select::ListItemTask::Owned = T
pub fn mdq::select::ListItemTask::clone_into(&self, target: &mut T)
pub fn mdq::select::ListItemTask::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::ListItemTask where T: 'static + ?core::marker::Sized
pub fn mdq::select::ListItemTask::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::ListItemTask where T: ?core::marker::Sized
pub fn mdq::select::ListItemTask::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::ListItemTask where T: ?core::marker::Sized
pub fn mdq::select::ListItemTask::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::ListItemTask where T: core::clone::Clone
pub unsafe fn mdq::select::ListItemTask::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::ListItemTask
pub fn mdq::select::ListItemTask::from(t: T) -> T
impl<T> pest::RuleType for mdq::select::ListItemTask where T: core::marker::Copy + core::fmt::Debug + core::cmp::Eq + core::hash::Hash + core::cmp::Ord
pub enum mdq::select::Matcher
pub mdq::select::Matcher::Any
pub mdq::select::Matcher::Any::explicit: bool
pub mdq::select::Matcher::Regex(mdq::select::Regex)
pub mdq::select::Matcher::Text
pub mdq::select::Matcher::Text::anchor_end: bool
pub mdq::select::Matcher::Text::anchor_start: bool
pub mdq::select::Matcher::Text::case_sensitive: bool
pub mdq::select::Matcher::Text::text: alloc::string::String
impl core::clone::Clone for mdq::select::Matcher
pub fn mdq::select::Matcher::clone(&self) -> mdq::select::Matcher
impl core::cmp::Eq for mdq::select::Matcher
impl core::cmp::Ord for mdq::select::Matcher
pub fn mdq::select::Matcher::cmp(&self, other: &mdq::select::Matcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::Matcher
pub fn mdq::select::Matcher::eq(&self, other: &mdq::select::Matcher) -> bool
impl core::cmp::PartialOrd for mdq::select::Matcher
pub fn mdq::select::Matcher::partial_cmp(&self, other: &mdq::select::Matcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::Matcher
pub fn mdq::select::Matcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::Matcher
pub fn mdq::select::Matcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::Matcher
impl core::marker::Freeze for mdq::select::Matcher
impl core::marker::Send for mdq::select::Matcher
impl core::marker::Sync for mdq::select::Matcher
impl core::marker::Unpin for mdq::select::Matcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::Matcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::Matcher
impl<T, U> core::convert::Into<U> for mdq::select::Matcher where U: core::convert::From<T>
pub fn mdq::select::Matcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::Matcher where U: core::convert::Into<T>
pub type mdq::select::Matcher::Error = core::convert::Infallible
pub fn mdq::select::Matcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::Matcher where U: core::convert::TryFrom<T>
pub type mdq::select::Matcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::Matcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::Matcher where T: core::clone::Clone
pub type mdq::select::Matcher::Owned = T
pub fn mdq::select::Matcher::clone_into(&self, target: &mut T)
pub fn mdq::select::Matcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::Matcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::Matcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::Matcher where T: ?core::marker::Sized
pub fn mdq::select::Matcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::Matcher where T: ?core::marker::Sized
pub fn mdq::select::Matcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::Matcher where T: core::clone::Clone
pub unsafe fn mdq::select::Matcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::Matcher
pub fn mdq::select::Matcher::from(t: T) -> T
pub enum mdq::select::Select
pub mdq::select::Select::Hit(alloc::vec::Vec<mdq::md_elem::MdElem>)
pub mdq::select::Select::Miss(mdq::md_elem::MdElem)
impl core::cmp::PartialEq for mdq::select::Select
pub fn mdq::select::Select::eq(&self, other: &mdq::select::Select) -> bool
impl core::fmt::Debug for mdq::select::Select
pub fn mdq::select::Select::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::marker::StructuralPartialEq for mdq::select::Select
impl core::marker::Freeze for mdq::select::Select
impl core::marker::Send for mdq::select::Select
impl core::marker::Sync for mdq::select::Select
impl core::marker::Unpin for mdq::select::Select
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::Select
impl core::panic::unwind_safe::UnwindSafe for mdq::select::Select
impl<T, U> core::convert::Into<U> for mdq::select::Select where U: core::convert::From<T>
pub fn mdq::select::Select::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::Select where U: core::convert::Into<T>
pub type mdq::select::Select::Error = core::convert::Infallible
pub fn mdq::select::Select::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::Select where U: core::convert::TryFrom<T>
pub type mdq::select::Select::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::Select::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> core::any::Any for mdq::select::Select where T: 'static + ?core::marker::Sized
pub fn mdq::select::Select::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::Select where T: ?core::marker::Sized
pub fn mdq::select::Select::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::Select where T: ?core::marker::Sized
pub fn mdq::select::Select::borrow_mut(&mut self) -> &mut T
impl<T> core::convert::From<T> for mdq::select::Select
pub fn mdq::select::Select::from(t: T) -> T
#[non_exhaustive] pub enum mdq::select::Selector
pub mdq::select::Selector::BlockQuote(mdq::select::BlockQuoteMatcher)
pub mdq::select::Selector::Chain(alloc::vec::Vec<Self>)
pub mdq::select::Selector::CodeBlock(mdq::select::CodeBlockMatcher)
pub mdq::select::Selector::FrontMatter(mdq::select::FrontMatterMatcher)
pub mdq::select::Selector::Html(mdq::select::HtmlMatcher)
pub mdq::select::Selector::Image(mdq::select::LinklikeMatcher)
pub mdq::select::Selector::Link(mdq::select::LinklikeMatcher)
pub mdq::select::Selector::ListItem(mdq::select::ListItemMatcher)
pub mdq::select::Selector::Paragraph(mdq::select::ParagraphMatcher)
pub mdq::select::Selector::Section(mdq::select::SectionMatcher)
pub mdq::select::Selector::Table(mdq::select::TableMatcher)
impl mdq::select::Selector
pub fn mdq::select::Selector::find_nodes(self, doc: mdq::md_elem::MdDoc) -> mdq::select::Result<(alloc::vec::Vec<mdq::md_elem::MdElem>, MdContext)>
impl core::clone::Clone for mdq::select::Selector
pub fn mdq::select::Selector::clone(&self) -> mdq::select::Selector
impl core::cmp::Eq for mdq::select::Selector
impl core::cmp::Ord for mdq::select::Selector
pub fn mdq::select::Selector::cmp(&self, other: &mdq::select::Selector) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::Selector
pub fn mdq::select::Selector::eq(&self, other: &mdq::select::Selector) -> bool
impl core::cmp::PartialOrd for mdq::select::Selector
pub fn mdq::select::Selector::partial_cmp(&self, other: &mdq::select::Selector) -> core::option::Option<core::cmp::Ordering>
impl core::convert::TryFrom<&alloc::string::String> for mdq::select::Selector
pub type mdq::select::Selector::Error = mdq::select::ParseError
pub fn mdq::select::Selector::try_from(value: &alloc::string::String) -> core::result::Result<Self, Self::Error>
impl core::convert::TryFrom<&str> for mdq::select::Selector
pub type mdq::select::Selector::Error = mdq::select::ParseError
pub fn mdq::select::Selector::try_from(value: &str) -> core::result::Result<Self, Self::Error>
impl core::fmt::Debug for mdq::select::Selector
pub fn mdq::select::Selector::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::Selector
pub fn mdq::select::Selector::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::Selector
impl core::marker::Freeze for mdq::select::Selector
impl core::marker::Send for mdq::select::Selector
impl core::marker::Sync for mdq::select::Selector
impl core::marker::Unpin for mdq::select::Selector
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::Selector
impl core::panic::unwind_safe::UnwindSafe for mdq::select::Selector
impl<T, U> core::convert::Into<U> for mdq::select::Selector where U: core::convert::From<T>
pub fn mdq::select::Selector::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::Selector where U: core::convert::Into<T>
pub type mdq::select::Selector::Error = core::convert::Infallible
pub fn mdq::select::Selector::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::Selector where U: core::convert::TryFrom<T>
pub type mdq::select::Selector::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::Selector::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::Selector where T: core::clone::Clone
pub type mdq::select::Selector::Owned = T
pub fn mdq::select::Selector::clone_into(&self, target: &mut T)
pub fn mdq::select::Selector::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::Selector where T: 'static + ?core::marker::Sized
pub fn mdq::select::Selector::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::Selector where T: ?core::marker::Sized
pub fn mdq::select::Selector::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::Selector where T: ?core::marker::Sized
pub fn mdq::select::Selector::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::Selector where T: core::clone::Clone
pub unsafe fn mdq::select::Selector::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::Selector
pub fn mdq::select::Selector::from(t: T) -> T
pub struct mdq::select::BlockQuoteMatcher
pub mdq::select::BlockQuoteMatcher::text: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::clone(&self) -> mdq::select::BlockQuoteMatcher
impl core::cmp::Eq for mdq::select::BlockQuoteMatcher
impl core::cmp::Ord for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::cmp(&self, other: &mdq::select::BlockQuoteMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::eq(&self, other: &mdq::select::BlockQuoteMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::partial_cmp(&self, other: &mdq::select::BlockQuoteMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::BlockQuoteMatcher
impl core::marker::Freeze for mdq::select::BlockQuoteMatcher
impl core::marker::Send for mdq::select::BlockQuoteMatcher
impl core::marker::Sync for mdq::select::BlockQuoteMatcher
impl core::marker::Unpin for mdq::select::BlockQuoteMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::BlockQuoteMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::BlockQuoteMatcher
impl<T, U> core::convert::Into<U> for mdq::select::BlockQuoteMatcher where U: core::convert::From<T>
pub fn mdq::select::BlockQuoteMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::BlockQuoteMatcher where U: core::convert::Into<T>
pub type mdq::select::BlockQuoteMatcher::Error = core::convert::Infallible
pub fn mdq::select::BlockQuoteMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::BlockQuoteMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::BlockQuoteMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::BlockQuoteMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::BlockQuoteMatcher where T: core::clone::Clone
pub type mdq::select::BlockQuoteMatcher::Owned = T
pub fn mdq::select::BlockQuoteMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::BlockQuoteMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::BlockQuoteMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::BlockQuoteMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::BlockQuoteMatcher where T: ?core::marker::Sized
pub fn mdq::select::BlockQuoteMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::BlockQuoteMatcher where T: ?core::marker::Sized
pub fn mdq::select::BlockQuoteMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::BlockQuoteMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::BlockQuoteMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::BlockQuoteMatcher
pub fn mdq::select::BlockQuoteMatcher::from(t: T) -> T
pub struct mdq::select::CodeBlockMatcher
pub mdq::select::CodeBlockMatcher::contents: mdq::select::MatchReplace
pub mdq::select::CodeBlockMatcher::language: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::clone(&self) -> mdq::select::CodeBlockMatcher
impl core::cmp::Eq for mdq::select::CodeBlockMatcher
impl core::cmp::Ord for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::cmp(&self, other: &mdq::select::CodeBlockMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::eq(&self, other: &mdq::select::CodeBlockMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::partial_cmp(&self, other: &mdq::select::CodeBlockMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::CodeBlockMatcher
impl core::marker::Freeze for mdq::select::CodeBlockMatcher
impl core::marker::Send for mdq::select::CodeBlockMatcher
impl core::marker::Sync for mdq::select::CodeBlockMatcher
impl core::marker::Unpin for mdq::select::CodeBlockMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::CodeBlockMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::CodeBlockMatcher
impl<T, U> core::convert::Into<U> for mdq::select::CodeBlockMatcher where U: core::convert::From<T>
pub fn mdq::select::CodeBlockMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::CodeBlockMatcher where U: core::convert::Into<T>
pub type mdq::select::CodeBlockMatcher::Error = core::convert::Infallible
pub fn mdq::select::CodeBlockMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::CodeBlockMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::CodeBlockMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::CodeBlockMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::CodeBlockMatcher where T: core::clone::Clone
pub type mdq::select::CodeBlockMatcher::Owned = T
pub fn mdq::select::CodeBlockMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::CodeBlockMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::CodeBlockMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::CodeBlockMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::CodeBlockMatcher where T: ?core::marker::Sized
pub fn mdq::select::CodeBlockMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::CodeBlockMatcher where T: ?core::marker::Sized
pub fn mdq::select::CodeBlockMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::CodeBlockMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::CodeBlockMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::CodeBlockMatcher
pub fn mdq::select::CodeBlockMatcher::from(t: T) -> T
pub struct mdq::select::FrontMatterMatcher
pub mdq::select::FrontMatterMatcher::text: mdq::select::MatchReplace
pub mdq::select::FrontMatterMatcher::variant: core::option::Option<mdq::md_elem::elem::FrontMatterVariant>
impl core::clone::Clone for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::clone(&self) -> mdq::select::FrontMatterMatcher
impl core::cmp::Eq for mdq::select::FrontMatterMatcher
impl core::cmp::Ord for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::cmp(&self, other: &mdq::select::FrontMatterMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::eq(&self, other: &mdq::select::FrontMatterMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::partial_cmp(&self, other: &mdq::select::FrontMatterMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::FrontMatterMatcher
impl core::marker::Freeze for mdq::select::FrontMatterMatcher
impl core::marker::Send for mdq::select::FrontMatterMatcher
impl core::marker::Sync for mdq::select::FrontMatterMatcher
impl core::marker::Unpin for mdq::select::FrontMatterMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::FrontMatterMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::FrontMatterMatcher
impl<T, U> core::convert::Into<U> for mdq::select::FrontMatterMatcher where U: core::convert::From<T>
pub fn mdq::select::FrontMatterMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::FrontMatterMatcher where U: core::convert::Into<T>
pub type mdq::select::FrontMatterMatcher::Error = core::convert::Infallible
pub fn mdq::select::FrontMatterMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::FrontMatterMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::FrontMatterMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::FrontMatterMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::FrontMatterMatcher where T: core::clone::Clone
pub type mdq::select::FrontMatterMatcher::Owned = T
pub fn mdq::select::FrontMatterMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::FrontMatterMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::FrontMatterMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::FrontMatterMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::FrontMatterMatcher where T: ?core::marker::Sized
pub fn mdq::select::FrontMatterMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::FrontMatterMatcher where T: ?core::marker::Sized
pub fn mdq::select::FrontMatterMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::FrontMatterMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::FrontMatterMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::FrontMatterMatcher
pub fn mdq::select::FrontMatterMatcher::from(t: T) -> T
pub struct mdq::select::HtmlMatcher
pub mdq::select::HtmlMatcher::html: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::clone(&self) -> mdq::select::HtmlMatcher
impl core::cmp::Eq for mdq::select::HtmlMatcher
impl core::cmp::Ord for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::cmp(&self, other: &mdq::select::HtmlMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::eq(&self, other: &mdq::select::HtmlMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::partial_cmp(&self, other: &mdq::select::HtmlMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::HtmlMatcher
impl core::marker::Freeze for mdq::select::HtmlMatcher
impl core::marker::Send for mdq::select::HtmlMatcher
impl core::marker::Sync for mdq::select::HtmlMatcher
impl core::marker::Unpin for mdq::select::HtmlMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::HtmlMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::HtmlMatcher
impl<T, U> core::convert::Into<U> for mdq::select::HtmlMatcher where U: core::convert::From<T>
pub fn mdq::select::HtmlMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::HtmlMatcher where U: core::convert::Into<T>
pub type mdq::select::HtmlMatcher::Error = core::convert::Infallible
pub fn mdq::select::HtmlMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::HtmlMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::HtmlMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::HtmlMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::HtmlMatcher where T: core::clone::Clone
pub type mdq::select::HtmlMatcher::Owned = T
pub fn mdq::select::HtmlMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::HtmlMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::HtmlMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::HtmlMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::HtmlMatcher where T: ?core::marker::Sized
pub fn mdq::select::HtmlMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::HtmlMatcher where T: ?core::marker::Sized
pub fn mdq::select::HtmlMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::HtmlMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::HtmlMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::HtmlMatcher
pub fn mdq::select::HtmlMatcher::from(t: T) -> T
pub struct mdq::select::LinklikeMatcher
pub mdq::select::LinklikeMatcher::display_matcher: mdq::select::MatchReplace
pub mdq::select::LinklikeMatcher::url_matcher: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::clone(&self) -> mdq::select::LinklikeMatcher
impl core::cmp::Eq for mdq::select::LinklikeMatcher
impl core::cmp::Ord for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::cmp(&self, other: &mdq::select::LinklikeMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::eq(&self, other: &mdq::select::LinklikeMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::partial_cmp(&self, other: &mdq::select::LinklikeMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::LinklikeMatcher
impl core::marker::Freeze for mdq::select::LinklikeMatcher
impl core::marker::Send for mdq::select::LinklikeMatcher
impl core::marker::Sync for mdq::select::LinklikeMatcher
impl core::marker::Unpin for mdq::select::LinklikeMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::LinklikeMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::LinklikeMatcher
impl<T, U> core::convert::Into<U> for mdq::select::LinklikeMatcher where U: core::convert::From<T>
pub fn mdq::select::LinklikeMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::LinklikeMatcher where U: core::convert::Into<T>
pub type mdq::select::LinklikeMatcher::Error = core::convert::Infallible
pub fn mdq::select::LinklikeMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::LinklikeMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::LinklikeMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::LinklikeMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::LinklikeMatcher where T: core::clone::Clone
pub type mdq::select::LinklikeMatcher::Owned = T
pub fn mdq::select::LinklikeMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::LinklikeMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::LinklikeMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::LinklikeMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::LinklikeMatcher where T: ?core::marker::Sized
pub fn mdq::select::LinklikeMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::LinklikeMatcher where T: ?core::marker::Sized
pub fn mdq::select::LinklikeMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::LinklikeMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::LinklikeMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::LinklikeMatcher
pub fn mdq::select::LinklikeMatcher::from(t: T) -> T
pub struct mdq::select::ListItemMatcher
pub mdq::select::ListItemMatcher::matcher: mdq::select::MatchReplace
pub mdq::select::ListItemMatcher::ordered: bool
pub mdq::select::ListItemMatcher::task: mdq::select::ListItemTask
impl core::clone::Clone for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::clone(&self) -> mdq::select::ListItemMatcher
impl core::cmp::Eq for mdq::select::ListItemMatcher
impl core::cmp::Ord for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::cmp(&self, other: &mdq::select::ListItemMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::eq(&self, other: &mdq::select::ListItemMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::partial_cmp(&self, other: &mdq::select::ListItemMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::ListItemMatcher
impl core::marker::Freeze for mdq::select::ListItemMatcher
impl core::marker::Send for mdq::select::ListItemMatcher
impl core::marker::Sync for mdq::select::ListItemMatcher
impl core::marker::Unpin for mdq::select::ListItemMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::ListItemMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::ListItemMatcher
impl<T, U> core::convert::Into<U> for mdq::select::ListItemMatcher where U: core::convert::From<T>
pub fn mdq::select::ListItemMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::ListItemMatcher where U: core::convert::Into<T>
pub type mdq::select::ListItemMatcher::Error = core::convert::Infallible
pub fn mdq::select::ListItemMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::ListItemMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::ListItemMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::ListItemMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::ListItemMatcher where T: core::clone::Clone
pub type mdq::select::ListItemMatcher::Owned = T
pub fn mdq::select::ListItemMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::ListItemMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::ListItemMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::ListItemMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::ListItemMatcher where T: ?core::marker::Sized
pub fn mdq::select::ListItemMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::ListItemMatcher where T: ?core::marker::Sized
pub fn mdq::select::ListItemMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::ListItemMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::ListItemMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::ListItemMatcher
pub fn mdq::select::ListItemMatcher::from(t: T) -> T
pub struct mdq::select::MatchReplace
pub mdq::select::MatchReplace::matcher: mdq::select::Matcher
pub mdq::select::MatchReplace::replacement: core::option::Option<alloc::string::String>
impl core::clone::Clone for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::clone(&self) -> mdq::select::MatchReplace
impl core::cmp::Eq for mdq::select::MatchReplace
impl core::cmp::Ord for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::cmp(&self, other: &mdq::select::MatchReplace) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::eq(&self, other: &mdq::select::MatchReplace) -> bool
impl core::cmp::PartialOrd for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::partial_cmp(&self, other: &mdq::select::MatchReplace) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::MatchReplace
impl core::marker::Freeze for mdq::select::MatchReplace
impl core::marker::Send for mdq::select::MatchReplace
impl core::marker::Sync for mdq::select::MatchReplace
impl core::marker::Unpin for mdq::select::MatchReplace
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::MatchReplace
impl core::panic::unwind_safe::UnwindSafe for mdq::select::MatchReplace
impl<T, U> core::convert::Into<U> for mdq::select::MatchReplace where U: core::convert::From<T>
pub fn mdq::select::MatchReplace::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::MatchReplace where U: core::convert::Into<T>
pub type mdq::select::MatchReplace::Error = core::convert::Infallible
pub fn mdq::select::MatchReplace::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::MatchReplace where U: core::convert::TryFrom<T>
pub type mdq::select::MatchReplace::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::MatchReplace::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::MatchReplace where T: core::clone::Clone
pub type mdq::select::MatchReplace::Owned = T
pub fn mdq::select::MatchReplace::clone_into(&self, target: &mut T)
pub fn mdq::select::MatchReplace::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::MatchReplace where T: 'static + ?core::marker::Sized
pub fn mdq::select::MatchReplace::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::MatchReplace where T: ?core::marker::Sized
pub fn mdq::select::MatchReplace::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::MatchReplace where T: ?core::marker::Sized
pub fn mdq::select::MatchReplace::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::MatchReplace where T: core::clone::Clone
pub unsafe fn mdq::select::MatchReplace::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::MatchReplace
pub fn mdq::select::MatchReplace::from(t: T) -> T
pub struct mdq::select::ParagraphMatcher
pub mdq::select::ParagraphMatcher::text: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::clone(&self) -> mdq::select::ParagraphMatcher
impl core::cmp::Eq for mdq::select::ParagraphMatcher
impl core::cmp::Ord for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::cmp(&self, other: &mdq::select::ParagraphMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::eq(&self, other: &mdq::select::ParagraphMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::partial_cmp(&self, other: &mdq::select::ParagraphMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::ParagraphMatcher
impl core::marker::Freeze for mdq::select::ParagraphMatcher
impl core::marker::Send for mdq::select::ParagraphMatcher
impl core::marker::Sync for mdq::select::ParagraphMatcher
impl core::marker::Unpin for mdq::select::ParagraphMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::ParagraphMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::ParagraphMatcher
impl<T, U> core::convert::Into<U> for mdq::select::ParagraphMatcher where U: core::convert::From<T>
pub fn mdq::select::ParagraphMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::ParagraphMatcher where U: core::convert::Into<T>
pub type mdq::select::ParagraphMatcher::Error = core::convert::Infallible
pub fn mdq::select::ParagraphMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::ParagraphMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::ParagraphMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::ParagraphMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::ParagraphMatcher where T: core::clone::Clone
pub type mdq::select::ParagraphMatcher::Owned = T
pub fn mdq::select::ParagraphMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::ParagraphMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::ParagraphMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::ParagraphMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::ParagraphMatcher where T: ?core::marker::Sized
pub fn mdq::select::ParagraphMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::ParagraphMatcher where T: ?core::marker::Sized
pub fn mdq::select::ParagraphMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::ParagraphMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::ParagraphMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::ParagraphMatcher
pub fn mdq::select::ParagraphMatcher::from(t: T) -> T
pub struct mdq::select::ParseError
pub mdq::select::ParseError::inner: mdq::query::error::InnerParseError
impl mdq::select::ParseError
pub fn mdq::select::ParseError::new(inner: mdq::query::error::InnerParseError) -> Self
impl mdq::select::ParseError
pub fn mdq::select::ParseError::to_string(&self, query_text: &str) -> alloc::string::String
impl core::clone::Clone for mdq::select::ParseError
pub fn mdq::select::ParseError::clone(&self) -> mdq::select::ParseError
impl core::cmp::Eq for mdq::select::ParseError
impl core::cmp::PartialEq for mdq::select::ParseError
pub fn mdq::select::ParseError::eq(&self, other: &mdq::select::ParseError) -> bool
impl core::error::Error for mdq::select::ParseError
pub fn mdq::select::ParseError::source(&self) -> core::option::Option<&(dyn core::error::Error + 'static)>
impl core::fmt::Debug for mdq::select::ParseError
pub fn mdq::select::ParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::select::ParseError
pub fn mdq::select::ParseError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::ParseError
pub fn mdq::select::ParseError::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::ParseError
impl core::marker::Freeze for mdq::select::ParseError
impl !core::marker::Send for mdq::select::ParseError
impl !core::marker::Sync for mdq::select::ParseError
impl core::marker::Unpin for mdq::select::ParseError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::ParseError
impl core::panic::unwind_safe::UnwindSafe for mdq::select::ParseError
impl<T, U> core::convert::Into<U> for mdq::select::ParseError where U: core::convert::From<T>
pub fn mdq::select::ParseError::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::ParseError where U: core::convert::Into<T>
pub type mdq::select::ParseError::Error = core::convert::Infallible
pub fn mdq::select::ParseError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::ParseError where U: core::convert::TryFrom<T>
pub type mdq::select::ParseError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::ParseError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::ParseError where T: core::clone::Clone
pub type mdq::select::ParseError::Owned = T
pub fn mdq::select::ParseError::clone_into(&self, target: &mut T)
pub fn mdq::select::ParseError::to_owned(&self) -> T
impl<T> alloc::string::ToString for mdq::select::ParseError where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::select::ParseError::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::select::ParseError where T: 'static + ?core::marker::Sized
pub fn mdq::select::ParseError::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::ParseError where T: ?core::marker::Sized
pub fn mdq::select::ParseError::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::ParseError where T: ?core::marker::Sized
pub fn mdq::select::ParseError::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::ParseError where T: core::clone::Clone
pub unsafe fn mdq::select::ParseError::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::ParseError
pub fn mdq::select::ParseError::from(t: T) -> T
pub struct mdq::select::Regex
pub mdq::select::Regex::re: fancy_regex::Regex
impl core::clone::Clone for mdq::select::Regex
pub fn mdq::select::Regex::clone(&self) -> mdq::select::Regex
impl core::cmp::Eq for mdq::select::Regex
impl core::cmp::Ord for mdq::select::Regex
pub fn mdq::select::Regex::cmp(&self, other: &Self) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::Regex
pub fn mdq::select::Regex::eq(&self, other: &Self) -> bool
impl core::cmp::PartialOrd for mdq::select::Regex
pub fn mdq::select::Regex::partial_cmp(&self, other: &Self) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::Regex
pub fn mdq::select::Regex::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::Regex
pub fn mdq::select::Regex::hash<H: core::hash::Hasher>(&self, state: &mut H)
impl core::marker::Freeze for mdq::select::Regex
impl core::marker::Send for mdq::select::Regex
impl core::marker::Sync for mdq::select::Regex
impl core::marker::Unpin for mdq::select::Regex
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::Regex
impl core::panic::unwind_safe::UnwindSafe for mdq::select::Regex
impl<T, U> core::convert::Into<U> for mdq::select::Regex where U: core::convert::From<T>
pub fn mdq::select::Regex::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::Regex where U: core::convert::Into<T>
pub type mdq::select::Regex::Error = core::convert::Infallible
pub fn mdq::select::Regex::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::Regex where U: core::convert::TryFrom<T>
pub type mdq::select::Regex::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::Regex::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::Regex where T: core::clone::Clone
pub type mdq::select::Regex::Owned = T
pub fn mdq::select::Regex::clone_into(&self, target: &mut T)
pub fn mdq::select::Regex::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::Regex where T: 'static + ?core::marker::Sized
pub fn mdq::select::Regex::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::Regex where T: ?core::marker::Sized
pub fn mdq::select::Regex::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::Regex where T: ?core::marker::Sized
pub fn mdq::select::Regex::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::Regex where T: core::clone::Clone
pub unsafe fn mdq::select::Regex::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::Regex
pub fn mdq::select::Regex::from(t: T) -> T
pub struct mdq::select::SectionMatcher
pub mdq::select::SectionMatcher::title: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::clone(&self) -> mdq::select::SectionMatcher
impl core::cmp::Eq for mdq::select::SectionMatcher
impl core::cmp::Ord for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::cmp(&self, other: &mdq::select::SectionMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::eq(&self, other: &mdq::select::SectionMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::partial_cmp(&self, other: &mdq::select::SectionMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::SectionMatcher
impl core::marker::Freeze for mdq::select::SectionMatcher
impl core::marker::Send for mdq::select::SectionMatcher
impl core::marker::Sync for mdq::select::SectionMatcher
impl core::marker::Unpin for mdq::select::SectionMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::SectionMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::SectionMatcher
impl<T, U> core::convert::Into<U> for mdq::select::SectionMatcher where U: core::convert::From<T>
pub fn mdq::select::SectionMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::SectionMatcher where U: core::convert::Into<T>
pub type mdq::select::SectionMatcher::Error = core::convert::Infallible
pub fn mdq::select::SectionMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::SectionMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::SectionMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::SectionMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::SectionMatcher where T: core::clone::Clone
pub type mdq::select::SectionMatcher::Owned = T
pub fn mdq::select::SectionMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::SectionMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::SectionMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::SectionMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::SectionMatcher where T: ?core::marker::Sized
pub fn mdq::select::SectionMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::SectionMatcher where T: ?core::marker::Sized
pub fn mdq::select::SectionMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::SectionMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::SectionMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::SectionMatcher
pub fn mdq::select::SectionMatcher::from(t: T) -> T
pub struct mdq::select::SelectError
pub struct mdq::select::SelectError
impl mdq::select::SelectError
impl mdq::select::SelectError
pub fn mdq::select::SelectError::new(message: impl core::convert::Into<alloc::string::String>) -> Self
pub fn mdq::select::SelectError::new(message: impl core::convert::Into<alloc::string::String>) -> Self
impl core::clone::Clone for mdq::select::SelectError
impl core::clone::Clone for mdq::select::SelectError
pub fn mdq::select::SelectError::clone(&self) -> mdq::select::SelectError
pub fn mdq::select::SelectError::clone(&self) -> mdq::select::SelectError
impl core::cmp::Eq for mdq::select::SelectError
impl core::cmp::Eq for mdq::select::SelectError
impl core::cmp::PartialEq for mdq::select::SelectError
impl core::cmp::PartialEq for mdq::select::SelectError
pub fn mdq::select::SelectError::eq(&self, other: &mdq::select::SelectError) -> bool
pub fn mdq::select::SelectError::eq(&self, other: &mdq::select::SelectError) -> bool
impl core::error::Error for mdq::select::SelectError
impl core::error::Error for mdq::select::SelectError
impl core::fmt::Debug for mdq::select::SelectError
impl core::fmt::Debug for mdq::select::SelectError
pub fn mdq::select::SelectError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::SelectError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::fmt::Display for mdq::select::SelectError
impl core::fmt::Display for mdq::select::SelectError
pub fn mdq::select::SelectError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
pub fn mdq::select::SelectError::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::SelectError
impl core::hash::Hash for mdq::select::SelectError
pub fn mdq::select::SelectError::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
pub fn mdq::select::SelectError::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::SelectError
impl core::marker::StructuralPartialEq for mdq::select::SelectError
impl core::marker::Freeze for mdq::select::SelectError
impl core::marker::Freeze for mdq::select::SelectError
impl core::marker::Send for mdq::select::SelectError
impl core::marker::Send for mdq::select::SelectError
impl core::marker::Sync for mdq::select::SelectError
impl core::marker::Sync for mdq::select::SelectError
impl core::marker::Unpin for mdq::select::SelectError
impl core::marker::Unpin for mdq::select::SelectError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::SelectError
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::SelectError
impl core::panic::unwind_safe::UnwindSafe for mdq::select::SelectError
impl core::panic::unwind_safe::UnwindSafe for mdq::select::SelectError
impl<T, U> core::convert::Into<U> for mdq::select::SelectError where U: core::convert::From<T>
impl<T, U> core::convert::Into<U> for mdq::select::SelectError where U: core::convert::From<T>
pub fn mdq::select::SelectError::into(self) -> U
pub fn mdq::select::SelectError::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::SelectError where U: core::convert::Into<T>
impl<T, U> core::convert::TryFrom<U> for mdq::select::SelectError where U: core::convert::Into<T>
pub type mdq::select::SelectError::Error = core::convert::Infallible
pub type mdq::select::SelectError::Error = core::convert::Infallible
pub fn mdq::select::SelectError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
pub fn mdq::select::SelectError::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::SelectError where U: core::convert::TryFrom<T>
impl<T, U> core::convert::TryInto<U> for mdq::select::SelectError where U: core::convert::TryFrom<T>
pub type mdq::select::SelectError::Error = <U as core::convert::TryFrom<T>>::Error
pub type mdq::select::SelectError::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::SelectError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
pub fn mdq::select::SelectError::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::SelectError where T: core::clone::Clone
impl<T> alloc::borrow::ToOwned for mdq::select::SelectError where T: core::clone::Clone
pub type mdq::select::SelectError::Owned = T
pub type mdq::select::SelectError::Owned = T
pub fn mdq::select::SelectError::clone_into(&self, target: &mut T)
pub fn mdq::select::SelectError::clone_into(&self, target: &mut T)
pub fn mdq::select::SelectError::to_owned(&self) -> T
pub fn mdq::select::SelectError::to_owned(&self) -> T
impl<T> alloc::string::ToString for mdq::select::SelectError where T: core::fmt::Display + ?core::marker::Sized
impl<T> alloc::string::ToString for mdq::select::SelectError where T: core::fmt::Display + ?core::marker::Sized
pub fn mdq::select::SelectError::to_string(&self) -> alloc::string::String
pub fn mdq::select::SelectError::to_string(&self) -> alloc::string::String
impl<T> core::any::Any for mdq::select::SelectError where T: 'static + ?core::marker::Sized
impl<T> core::any::Any for mdq::select::SelectError where T: 'static + ?core::marker::Sized
pub fn mdq::select::SelectError::type_id(&self) -> core::any::TypeId
pub fn mdq::select::SelectError::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::SelectError where T: ?core::marker::Sized
impl<T> core::borrow::Borrow<T> for mdq::select::SelectError where T: ?core::marker::Sized
pub fn mdq::select::SelectError::borrow(&self) -> &T
pub fn mdq::select::SelectError::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::SelectError where T: ?core::marker::Sized
impl<T> core::borrow::BorrowMut<T> for mdq::select::SelectError where T: ?core::marker::Sized
pub fn mdq::select::SelectError::borrow_mut(&mut self) -> &mut T
pub fn mdq::select::SelectError::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::SelectError where T: core::clone::Clone
impl<T> core::clone::CloneToUninit for mdq::select::SelectError where T: core::clone::Clone
pub unsafe fn mdq::select::SelectError::clone_to_uninit(&self, dest: *mut u8)
pub unsafe fn mdq::select::SelectError::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::SelectError
impl<T> core::convert::From<T> for mdq::select::SelectError
pub fn mdq::select::SelectError::from(t: T) -> T
pub fn mdq::select::SelectError::from(t: T) -> T
pub struct mdq::select::TableMatcher
pub mdq::select::TableMatcher::headers: mdq::select::MatchReplace
pub mdq::select::TableMatcher::rows: mdq::select::MatchReplace
impl core::clone::Clone for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::clone(&self) -> mdq::select::TableMatcher
impl core::cmp::Eq for mdq::select::TableMatcher
impl core::cmp::Ord for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::cmp(&self, other: &mdq::select::TableMatcher) -> core::cmp::Ordering
impl core::cmp::PartialEq for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::eq(&self, other: &mdq::select::TableMatcher) -> bool
impl core::cmp::PartialOrd for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::partial_cmp(&self, other: &mdq::select::TableMatcher) -> core::option::Option<core::cmp::Ordering>
impl core::fmt::Debug for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::fmt(&self, f: &mut core::fmt::Formatter<'_>) -> core::fmt::Result
impl core::hash::Hash for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::hash<__H: core::hash::Hasher>(&self, state: &mut __H)
impl core::marker::StructuralPartialEq for mdq::select::TableMatcher
impl core::marker::Freeze for mdq::select::TableMatcher
impl core::marker::Send for mdq::select::TableMatcher
impl core::marker::Sync for mdq::select::TableMatcher
impl core::marker::Unpin for mdq::select::TableMatcher
impl core::panic::unwind_safe::RefUnwindSafe for mdq::select::TableMatcher
impl core::panic::unwind_safe::UnwindSafe for mdq::select::TableMatcher
impl<T, U> core::convert::Into<U> for mdq::select::TableMatcher where U: core::convert::From<T>
pub fn mdq::select::TableMatcher::into(self) -> U
impl<T, U> core::convert::TryFrom<U> for mdq::select::TableMatcher where U: core::convert::Into<T>
pub type mdq::select::TableMatcher::Error = core::convert::Infallible
pub fn mdq::select::TableMatcher::try_from(value: U) -> core::result::Result<T, <T as core::convert::TryFrom<U>>::Error>
impl<T, U> core::convert::TryInto<U> for mdq::select::TableMatcher where U: core::convert::TryFrom<T>
pub type mdq::select::TableMatcher::Error = <U as core::convert::TryFrom<T>>::Error
pub fn mdq::select::TableMatcher::try_into(self) -> core::result::Result<U, <U as core::convert::TryFrom<T>>::Error>
impl<T> alloc::borrow::ToOwned for mdq::select::TableMatcher where T: core::clone::Clone
pub type mdq::select::TableMatcher::Owned = T
pub fn mdq::select::TableMatcher::clone_into(&self, target: &mut T)
pub fn mdq::select::TableMatcher::to_owned(&self) -> T
impl<T> core::any::Any for mdq::select::TableMatcher where T: 'static + ?core::marker::Sized
pub fn mdq::select::TableMatcher::type_id(&self) -> core::any::TypeId
impl<T> core::borrow::Borrow<T> for mdq::select::TableMatcher where T: ?core::marker::Sized
pub fn mdq::select::TableMatcher::borrow(&self) -> &T
impl<T> core::borrow::BorrowMut<T> for mdq::select::TableMatcher where T: ?core::marker::Sized
pub fn mdq::select::TableMatcher::borrow_mut(&mut self) -> &mut T
impl<T> core::clone::CloneToUninit for mdq::select::TableMatcher where T: core::clone::Clone
pub unsafe fn mdq::select::TableMatcher::clone_to_uninit(&self, dest: *mut u8)
impl<T> core::convert::From<T> for mdq::select::TableMatcher
pub fn mdq::select::TableMatcher::from(t: T) -> T
pub trait mdq::select::TrySelector<I: core::convert::Into<mdq::md_elem::MdElem>>
pub fn mdq::select::TrySelector::try_select(&self, ctx: &MdContext, item: I) -> mdq::select::Result<mdq::select::Select>
impl<I, M> mdq::select::TrySelector<I> for M where I: core::convert::Into<mdq::md_elem::MdElem>, M: MatchSelector<I>
pub fn M::try_select(&self, _: &MdContext, item: I) -> mdq::select::Result<mdq::select::Select>
pub type mdq::select::Result<T> = core::result::Result<T, mdq::select::SelectError>
pub type mdq::select::Result<T> = core::result::Result<T, mdq::select::SelectError>
