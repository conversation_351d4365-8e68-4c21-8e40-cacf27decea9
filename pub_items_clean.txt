InputStream:75:pub mod md_elem;
InputStream:76:pub mod output;
InputStream:78:pub mod run;
InputStream:79:pub mod select;
InputStream:1:pub trait Concatenate: Sized {
InputStream:7:pub use tree::*;
InputStream:31:pub struct MdContext {
InputStream:45:    pub fn get_footnote(&self, footnote_id: &FootnoteId) -> &Vec<MdElem> {
InputStream:63:pub struct MdDoc {
InputStream:64:    pub roots: Vec<MdElem>,
InputStream:65:    pub ctx: MdContext,
InputStream:72:    pub fn parse(text: &str, options: &ParseOptions) -> Result<Self, InvalidMd> {
InputStream:92:pub enum MdElem {
InputStream:159:pub struct ParseOptions {
InputStream:166:    pub allow_unknown_markdown: bool,
InputStream:178:    pub fn gfm() -> Self {
InputStream:218:    pub validate_no_conflicting_links: bool,
InputStream:220:    pub allow_unknown_markdown: bool,
InputStream:225:pub enum InvalidMd {
InputStream:258:pub struct MarkdownPart {
InputStream:268:pub struct UnknownMdParseError {
InputStream:346:pub mod elem {
InputStream:353:    pub type TableRow = Vec<TableCell>;
InputStream:358:    pub type TableCell = Vec<Inline>;
InputStream:362:    pub enum CodeVariant {
InputStream:388:    pub enum Inline {
InputStream:483:    pub struct BlockHtml {
InputStream:484:        pub value: String,
InputStream:488:    pub struct FrontMatter {
InputStream:489:        pub variant: FrontMatterVariant,
InputStream:490:        pub body: String,
InputStream:494:    pub enum FrontMatterVariant {
InputStream:508:        pub fn name(self) -> &'static str {
InputStream:523:        pub fn separator(self) -> &'static str {
InputStream:584:    pub struct Span {
InputStream:585:        pub variant: crate::md_elem::tree::elem::SpanVariant,
InputStream:586:        pub children: Vec<crate::md_elem::tree::elem::Inline>,
InputStream:644:    pub struct Text {
InputStream:645:        pub variant: crate::md_elem::tree::elem::TextVariant,
InputStream:646:        pub value: String,
InputStream:685:    pub struct Link {
InputStream:686:        pub display: Vec<crate::md_elem::tree::elem::Inline>,
InputStream:687:        pub link: crate::md_elem::tree::elem::LinkDefinition,
InputStream:720:    pub struct Image {
InputStream:721:        pub alt: String,
InputStream:722:        pub link: crate::md_elem::tree::elem::LinkDefinition,
InputStream:770:    pub struct FootnoteId {
InputStream:771:        pub id: String,
InputStream:794:        pub fn as_str(&self) -> &str {
InputStream:808:    pub struct LinkDefinition {
InputStream:810:        pub url: String,
InputStream:814:        pub title: Option<String>,
InputStream:816:        pub reference: LinkReference,
InputStream:823:    pub struct ListItem {
InputStream:826:        pub checked: Option<bool>,
InputStream:827:        pub item: Vec<MdElem>,
InputStream:832:    pub enum SpanVariant {
InputStream:840:    pub enum TextVariant {
InputStream:943:    pub struct Section {
InputStream:944:        pub depth: u8,
InputStream:945:        pub title: Vec<Inline>,
InputStream:946:        pub body: Vec<MdElem>,
InputStream:971:    pub struct Paragraph {
InputStream:972:        pub body: Vec<Inline>,
InputStream:1001:    pub struct BlockQuote {
InputStream:1002:        pub body: Vec<MdElem>,
InputStream:1075:    pub struct List {
InputStream:1076:        pub starting_index: Option<u32>,
InputStream:1077:        pub items: Vec<ListItem>,
InputStream:1120:    pub struct Table {
InputStream:1121:        pub alignments: Vec<Option<ColumnAlignment>>,
InputStream:1126:        pub rows: Vec<TableRow>,
InputStream:1134:    pub enum ColumnAlignment {
InputStream:1168:    pub struct CodeBlock {
InputStream:1170:        pub variant: CodeVariant,
InputStream:1172:        pub value: String,
InputStream:1179:    pub enum LinkReference {
InputStream:1229:    pub struct CodeOpts {
InputStream:1230:        pub language: String,
InputStream:1231:        pub metadata: Option<String>,
InputStream:1806:        pub fn empty() -> Self {
InputStream:1813:        pub fn with<S: Into<FootnoteId>>(mut self, footnote_id: S, body: Vec<MdElem>) -> Self {
InputStream:9:        pub fn alignments(&self) -> &[Option<ColumnAlignment>] {
InputStream:13:        pub fn rows(&self) -> &Vec<TableRow> {
InputStream:23:        pub fn normalize(&mut self) {
InputStream:40:        pub fn retain_columns_by_header<F, E>(&mut self, mut f: F) -> Result<(), E>
InputStream:70:        pub fn retain_rows<F, E>(&mut self, mut f: F) -> Result<(), E>
InputStream:87:        pub fn is_empty(&self) -> bool {
InputStream:14:pub struct MdWriterOptions {
InputStream:16:    pub link_reference_placement: ReferencePlacement,
InputStream:18:    pub footnote_reference_placement: ReferencePlacement,
InputStream:20:    pub inline_options: InlineElemOptions,
InputStream:35:    pub include_thematic_breaks: bool,
InputStream:53:    pub text_width: Option<usize>,
InputStream:58:pub enum ReferencePlacement {
InputStream:157:    pub fn write_one_md<W>(&mut self, out: &mut Output<W>, node_ref: &'md MdElem)
InputStream:531:pub mod tests {
InputStream:14:pub struct InlineElemOptions {
InputStream:15:    pub link_format: LinkTransform,
InputStream:16:    pub renumber_footnotes: bool,
InputStream:29:    pub links: HashMap<LinkLabel<'md>, UrlAndTitle<'md>>,
InputStream:30:    pub footnotes: HashSet<&'md FootnoteId>,
InputStream:44:    pub url: &'md String,
InputStream:46:    pub title: &'md Option<String>,
InputStream:76:    pub fn new(ctx: &'md MdContext, options: InlineElemOptions) -> Self {
InputStream:88:    pub fn has_pending_links(&self) -> bool {
InputStream:92:    pub fn has_pending_footnotes(&self) -> bool {
InputStream:96:    pub fn count_pending_links(&self) -> usize {
InputStream:100:    pub fn count_pending_footnotes(&self) -> usize {
InputStream:104:    pub fn drain_pending_links(&mut self) -> Vec<(LinkLabel<'md>, UrlAndTitle<'md>)> {
InputStream:108:    pub fn drain_pending_footnotes(&mut self) -> Vec<(String, &'md Vec<MdElem>)> {
InputStream:121:    pub fn write_line<I, W>(&mut self, out: &mut Output<W>, elems: I)
InputStream:131:    pub fn write_inline_element<W>(&mut self, out: &mut Output<W>, elem: &'md Inline)
InputStream:272:    pub fn write_linklike<W, L>(&mut self, out: &mut Output<W>, link_like: L)
InputStream:375:    pub fn write_url_title<W>(&mut self, out: &mut Output<W>, title: &Option<String>)
InputStream:418:    pub fn find_best_strategy(text: &str) -> Self {
InputStream:7:pub struct PlainWriterOptions {
InputStream:8:    pub include_breaks: bool,
InputStream:19:pub struct PlainWriter {
InputStream:25:    pub fn with_options(options: PlainWriterOptions) -> Self {
InputStream:30:    pub fn write<'md, I, W>(&self, nodes: I, out: &mut W)
InputStream:4:pub struct NewlineCollapser<W> {
InputStream:15:    pub fn new(underlying: W, max_newlines: usize) -> Self {
InputStream:23:    pub fn have_pending_newlines(&self) -> bool {
InputStream:30:    pub fn take_underlying(self) -> W {
InputStream:4:pub struct FootnoteTransformer<'md> {
InputStream:8:pub struct FootnoteTransformerToString<'a, 'md> {
InputStream:14:    pub fn new(active: bool) -> Self {
InputStream:20:    pub fn write<W>(&mut self, out: &mut Output<W>, label: &'md str)
InputStream:34:    pub fn new_to_stringer<'a>(&'a mut self) -> FootnoteTransformerToString<'a, 'md> {
InputStream:40:    pub fn transform(&mut self, label: &'md str) -> String {
InputStream:13:pub enum LinkTransform {
InputStream:39:    pub fn get_sort_string(&self, ctx: &'md MdContext) -> String {
InputStream:127:    pub fn transform_variant(&self) -> LinkTransform {
InputStream:14:pub use crate::output::fmt_md::*;
InputStream:15:pub use crate::output::fmt_md_inlines::*;
InputStream:16:pub use crate::output::link_transform::*;
InputStream:17:pub use crate::output::output_adapter::*;
InputStream:18:pub use crate::output::tree_ref_serde::*;
InputStream:20:pub use crate::output::fmt_plain_inline::*;
InputStream:8:pub struct MdWriter {
InputStream:14:    pub fn with_options(options: MdWriterOptions) -> Self {
InputStream:19:    pub fn write<'md, I, W>(&self, ctx: &'md MdContext, nodes: I, out: &mut W)
InputStream:49:pub struct IoAdapter<W>(pub W);
InputStream:12:pub struct SerializableMd<'md> {
InputStream:152:    pub fn new(elems: &'md [MdElem], ctx: &'md MdContext, opts: InlineElemOptions) -> Self {
InputStream:11:pub struct ParseError {
InputStream:78:    pub fn to_string(&self, query_text: &str) -> String {
InputStream:100:pub struct DetachedSpan {
InputStream:101:    pub start: usize,
InputStream:102:    pub end: usize,
InputStream:10:pub use error::*;
InputStream:13:pub use pest::StringVariant;
InputStream:2:pub use crate::query::pest::test_helpers::StringVariant;
InputStream:12:pub struct Query {
InputStream:20:pub struct Error {
InputStream:54:    pub fn parse(query_text: &str) -> Result<Pairs, Error> {
InputStream:117:    pub enum StringVariant {
InputStream:125:        pub fn parse(self, query_text: &str) -> Result<(Pairs, &str), Error> {
InputStream:134:        pub fn as_rule(self) -> Rule {
InputStream:6:pub struct ParsedString {
InputStream:7:    pub text: String,
InputStream:8:    pub anchor_start: bool,
InputStream:9:    pub anchor_end: bool,
InputStream:10:    pub mode: ParsedStringMode,
InputStream:11:    pub explicit_wildcard: bool,
InputStream:12:    pub replace_string: Option<String>,
InputStream:16:pub enum ParsedStringMode {
InputStream:24:    pub fn is_equivalent_to_asterisk(&self) -> bool {
InputStream:3:pub type OnePair<'a> = OneOf<Pair<'a>>;
InputStream:6:pub trait PairMatcher {
InputStream:21:pub trait PairMatchStore<'a> {
InputStream:44:pub enum MatchStoreResult<'a> {
InputStream:50:pub struct Present(bool);
InputStream:53:    pub fn is_present(&self) -> bool {
InputStream:59:    pub fn store(&mut self, _pair: Pair) {
InputStream:65:pub struct OneOf<T>(Result<Option<T>, ()>);
InputStream:74:    pub fn take(self) -> Result<Option<T>, String> {
InputStream:78:    pub fn store(&mut self, item: T) {
InputStream:87:pub struct FindAll<'a, M>(M, Vec<Pair<'a>>);
InputStream:90:    pub fn new(matcher: M) -> Self {
InputStream:115:pub struct ByRule(Rule);
InputStream:118:    pub fn new(rule: Rule) -> Self {
InputStream:130:pub struct ByTag(&'static str);
InputStream:133:    pub fn new(tag: &'static str) -> Self {
InputStream:27:        pub struct $finder_name {
InputStream:34:        pub struct $result_name<'a> {
InputStream:36:                pub $elem: $result,
InputStream:51:            pub fn traverse(pairs: Pairs) -> $result_name {
InputStream:13:            pub $name:ident : $ty:ty
InputStream:19:        pub struct CliOptions {
InputStream:78:        pub struct RunOptions {
InputStream:81:            pub $name: $ty,
InputStream:86:            pub add_breaks: Option<bool>,
InputStream:88:            pub selectors: String,
InputStream:90:            pub markdown_file_paths: Vec<String>
InputStream:134:    pub link_pos: ReferencePlacement,
InputStream:138:    pub footnote_pos: Option<ReferencePlacement>,
InputStream:141:    pub link_format: LinkTransform,
InputStream:144:    pub renumber_footnotes: bool,
InputStream:148:    pub output: OutputFormat,
InputStream:157:    pub wrap_width: Option<usize>,
InputStream:161:    pub quiet: bool,
InputStream:165:    pub allow_unknown_markdown: bool,
InputStream:202:    pub fn should_add_breaks(&self) -> bool {
InputStream:212:    pub fn extra_validation(&self) -> bool {
InputStream:243:pub enum OutputFormat {
InputStream:55:pub use cli::*;
InputStream:56:pub use run_main::*;
InputStream:15:pub enum Error {
InputStream:37:pub struct QueryParseError {
InputStream:68:pub enum Input {
InputStream:122:pub trait OsFacade {
InputStream:172:pub fn run(cli: &RunOptions, os: &mut impl OsFacade) -> bool {
InputStream:31:pub struct SelectError {
InputStream:54:pub type Result<T> = std::result::Result<T, SelectError>;
InputStream:8:pub enum Matcher {
InputStream:30:pub struct Regex {
InputStream:4:pub struct MatchReplace {
InputStream:5:    pub matcher: Matcher,
InputStream:6:    pub replacement: Option<String>,
InputStream:8:pub trait MatchSelector<I> {
InputStream:23:pub use crate::query::ParseError;
InputStream:24:pub use api::{Result, SelectError};
InputStream:25:pub use match_replace::*;
InputStream:26:pub use matcher::*;
InputStream:27:pub use selector::*;
InputStream:8:pub enum ListItemTask {
InputStream:21:pub struct ListItemMatcher {
InputStream:23:    pub ordered: bool,
InputStream:27:    pub task: ListItemTask,
InputStream:28:    pub matcher: MatchReplace,
InputStream:33:pub struct SectionMatcher {
InputStream:34:    pub title: MatchReplace,
InputStream:39:pub struct LinklikeMatcher {
InputStream:40:    pub display_matcher: MatchReplace,
InputStream:41:    pub url_matcher: MatchReplace,
InputStream:46:pub struct BlockQuoteMatcher {
InputStream:47:    pub text: MatchReplace,
InputStream:52:pub struct HtmlMatcher {
InputStream:53:    pub html: MatchReplace,
InputStream:58:pub struct ParagraphMatcher {
InputStream:59:    pub text: MatchReplace,
InputStream:64:pub struct CodeBlockMatcher {
InputStream:65:    pub language: MatchReplace,
InputStream:66:    pub contents: MatchReplace,
InputStream:71:pub struct FrontMatterMatcher {
InputStream:72:    pub variant: Option<FrontMatterVariant>,
InputStream:73:    pub text: MatchReplace,
InputStream:78:pub struct TableMatcher {
InputStream:79:    pub headers: MatchReplace,
InputStream:80:    pub rows: MatchReplace,
InputStream:86:pub enum Selector {
InputStream:122:    pub fn find_nodes(self, doc: MdDoc) -> Result<(Vec<MdElem>, MdContext)> {
InputStream:5:pub struct ChainSelector {
InputStream:7:pub struct CodeBlockSelector {
InputStream:40:pub struct LinkSelector {
InputStream:82:pub struct ImageSelector {
InputStream:124:pub struct LinkMatchers {
InputStream:125:    pub display_matcher: StringMatcher,
InputStream:126:    pub url_matcher: StringMatcher,
InputStream:7:pub struct ListItemSelector {
InputStream:28:pub enum ListItemType {
InputStream:7:pub struct SectionSelector {
InputStream:11:            pub struct [<$name Selector>] {
InputStream:36:pub struct HtmlSelector {
InputStream:57:pub struct FrontMatterSelector {
InputStream:7:pub struct TableSelector {
InputStream:9:pub struct StringMatcher {
InputStream:48:    pub fn to_select_error(&self, selector_name: &str) -> SelectError {
InputStream:64:    pub fn matches(&self, haystack: &str) -> Result<bool, StringMatchError> {
InputStream:90:    pub fn matches_inlines<I: Borrow<Inline>>(&self, haystack: &[I]) -> Result<bool, StringMatchError> {
InputStream:94:    pub fn matches_any<N: Borrow<MdElem>>(&self, haystacks: &[N]) -> Result<bool, StringMatchError> {
InputStream:1:pub mod output;
InputStream:2:pub mod str_utils;
InputStream:3:pub mod utils_for_test;
InputStream:4:pub mod vec_utils;
InputStream:5:pub trait SimpleWrite {
InputStream:21:pub struct Stream<W>(pub W);
InputStream:42:pub struct Output<W: SimpleWrite> {
InputStream:60:pub struct PreWriter<'a, W: SimpleWrite> {
InputStream:73:pub enum Block {
InputStream:102:    pub fn new(to: W, text_width: Option<usize>) -> Self {
InputStream:111:    pub fn without_text_wrapping(to: W) -> Self {
InputStream:115:    pub fn replace_underlying(&mut self, new: W) -> std::io::Result<W> {
InputStream:120:    pub fn with_block(&mut self, block: Block, action: impl FnOnce(&mut Self)) {
InputStream:132:    pub fn with_pre_block(&mut self, action: impl FnOnce(&mut PreWriter<W>)) {
InputStream:143:    pub fn without_wrapping(&mut self, action: impl FnOnce(&mut Self)) {
InputStream:171:    pub fn write_str(&mut self, text: &str) {
InputStream:175:    pub fn write_char(&mut self, ch: char) {
InputStream:220:    pub fn take_underlying(&mut self) -> std::io::Result<W> {
InputStream:440:    pub fn write_str(&mut self, text: &str) {
InputStream:444:    pub fn write_char(&mut self, ch: char) {
InputStream:4:pub fn pad_to<W>(output: &mut Output<W>, input: &str, min_width: usize, alignment: Option<ColumnAlignment>)
InputStream:33:pub struct CountingWriter<'a, W> {
InputStream:39:    pub fn wrap(underlying: &'a mut W) -> Self {
InputStream:50:    pub fn count(&self) -> usize {
InputStream:14:        pub fn default_for_tests() -> Self {
InputStream:20:        pub fn default_for_tests() -> Self {
InputStream:26:        pub fn default_for_tests() -> Self {
InputStream:39:        pub fn new_with<F>(init: F) -> Self
InputStream:49:    pub fn get_only<T: Debug, C: IntoIterator<Item = T>>(col: C) -> T {
InputStream:144:                pub struct [<VariantsChecker $name:lower:camel>] {
InputStream:3:pub struct IndexKeeper {
InputStream:8:    pub fn new() -> Self {
InputStream:14:    pub fn retain_when<I, F, E>(&mut self, items: &[I], mut allow_filter: F) -> Result<(), E>
InputStream:27:    pub fn retain_fn<I, E>(&self) -> impl FnMut(usize, &I) -> Result<bool, E> + '_ {
InputStream:43:    pub fn count_keeps(&self) -> usize {
InputStream:48:pub trait ItemRetainer<I> {
InputStream:3:pub enum WordBoundary {
InputStream:8:pub struct WordsBuffer {
InputStream:26:pub trait BufferedCharWrite: FnMut(char) -> usize {}
InputStream:30:pub struct WordBoundaryRestore {
InputStream:35:    pub fn restore_to(self, to: &mut WordsBuffer) {
InputStream:41:    pub fn new(line_length: usize) -> Self {
InputStream:56:    pub fn disabled() -> Self {
InputStream:63:    pub fn set_word_boundary(&mut self, boundary: WordBoundary) -> WordBoundaryRestore {
InputStream:69:    pub fn shorten_current_line(&mut self, by: usize) {
InputStream:73:    pub fn push(&mut self, ch: char, mut action: impl BufferedCharWrite) {
InputStream:136:    pub fn has_pending_word(&self) -> bool {
InputStream:144:    pub fn drain_pending_word(&mut self, mut drain_action: impl BufferedCharWrite) {
InputStream:157:    pub fn reset(&mut self) {
InputStream:406:        pub fn push(&mut self, ch: char) {
InputStream:414:        pub fn push_str(&mut self, text: &str) {
InputStream:418:        pub fn end(mut self) -> String {
