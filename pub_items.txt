
src\lib.rs:75:pub mod md_elem;
src\lib.rs:76:pub mod output;
src\lib.rs:78:pub mod run;
src\lib.rs:79:pub mod select;
src\md_elem\concatenate.rs:1:pub trait Concatenate: Sized {
src\md_elem\mod.rs:7:pub use tree::*;
src\md_elem\tree.rs:31:pub struct MdContext {
src\md_elem\tree.rs:45:    pub fn get_footnote(&self, footnote_id: &FootnoteId) -> &Vec<MdElem> {
src\md_elem\tree.rs:63:pub struct MdDoc {
src\md_elem\tree.rs:64:    pub roots: Vec<MdElem>,
src\md_elem\tree.rs:65:    pub ctx: MdContext,
src\md_elem\tree.rs:72:    pub fn parse(text: &str, options: &ParseOptions) -> Result<Self, InvalidMd> {
src\md_elem\tree.rs:92:pub enum MdElem {
src\md_elem\tree.rs:159:pub struct ParseOptions {
src\md_elem\tree.rs:166:    pub allow_unknown_markdown: bool,
src\md_elem\tree.rs:178:    pub fn gfm() -> Self {
src\md_elem\tree.rs:218:    pub validate_no_conflicting_links: bool,
src\md_elem\tree.rs:220:    pub allow_unknown_markdown: bool,
src\md_elem\tree.rs:225:pub enum InvalidMd {
src\md_elem\tree.rs:258:pub struct MarkdownPart {
src\md_elem\tree.rs:268:pub struct UnknownMdParseError {
src\md_elem\tree.rs:346:pub mod elem {
src\md_elem\tree.rs:353:    pub type TableRow = Vec<TableCell>;
src\md_elem\tree.rs:358:    pub type TableCell = Vec<Inline>;
src\md_elem\tree.rs:362:    pub enum CodeVariant {
src\md_elem\tree.rs:388:    pub enum Inline {
src\md_elem\tree.rs:483:    pub struct BlockHtml {
src\md_elem\tree.rs:484:        pub value: String,
src\md_elem\tree.rs:488:    pub struct FrontMatter {
src\md_elem\tree.rs:489:        pub variant: FrontMatterVariant,
src\md_elem\tree.rs:490:        pub body: String,
src\md_elem\tree.rs:494:    pub enum FrontMatterVariant {
src\md_elem\tree.rs:508:        pub fn name(self) -> &'static str {
src\md_elem\tree.rs:523:        pub fn separator(self) -> &'static str {
src\md_elem\tree.rs:584:    pub struct Span {
src\md_elem\tree.rs:585:        pub variant: crate::md_elem::tree::elem::SpanVariant,
src\md_elem\tree.rs:586:        pub children: Vec<crate::md_elem::tree::elem::Inline>,
src\md_elem\tree.rs:644:    pub struct Text {
src\md_elem\tree.rs:645:        pub variant: crate::md_elem::tree::elem::TextVariant,
src\md_elem\tree.rs:646:        pub value: String,
src\md_elem\tree.rs:685:    pub struct Link {
src\md_elem\tree.rs:686:        pub display: Vec<crate::md_elem::tree::elem::Inline>,
src\md_elem\tree.rs:687:        pub link: crate::md_elem::tree::elem::LinkDefinition,
src\md_elem\tree.rs:720:    pub struct Image {
src\md_elem\tree.rs:721:        pub alt: String,
src\md_elem\tree.rs:722:        pub link: crate::md_elem::tree::elem::LinkDefinition,
src\md_elem\tree.rs:770:    pub struct FootnoteId {
src\md_elem\tree.rs:771:        pub id: String,
src\md_elem\tree.rs:794:        pub fn as_str(&self) -> &str {
src\md_elem\tree.rs:808:    pub struct LinkDefinition {
src\md_elem\tree.rs:810:        pub url: String,
src\md_elem\tree.rs:814:        pub title: Option<String>,
src\md_elem\tree.rs:816:        pub reference: LinkReference,
src\md_elem\tree.rs:823:    pub struct ListItem {
src\md_elem\tree.rs:826:        pub checked: Option<bool>,
src\md_elem\tree.rs:827:        pub item: Vec<MdElem>,
src\md_elem\tree.rs:832:    pub enum SpanVariant {
src\md_elem\tree.rs:840:    pub enum TextVariant {
src\md_elem\tree.rs:943:    pub struct Section {
src\md_elem\tree.rs:944:        pub depth: u8,
src\md_elem\tree.rs:945:        pub title: Vec<Inline>,
src\md_elem\tree.rs:946:        pub body: Vec<MdElem>,
src\md_elem\tree.rs:971:    pub struct Paragraph {
src\md_elem\tree.rs:972:        pub body: Vec<Inline>,
src\md_elem\tree.rs:1001:    pub struct BlockQuote {
src\md_elem\tree.rs:1002:        pub body: Vec<MdElem>,
src\md_elem\tree.rs:1075:    pub struct List {
src\md_elem\tree.rs:1076:        pub starting_index: Option<u32>,
src\md_elem\tree.rs:1077:        pub items: Vec<ListItem>,
src\md_elem\tree.rs:1120:    pub struct Table {
src\md_elem\tree.rs:1121:        pub alignments: Vec<Option<ColumnAlignment>>,
src\md_elem\tree.rs:1126:        pub rows: Vec<TableRow>,
src\md_elem\tree.rs:1134:    pub enum ColumnAlignment {
src\md_elem\tree.rs:1168:    pub struct CodeBlock {
src\md_elem\tree.rs:1170:        pub variant: CodeVariant,
src\md_elem\tree.rs:1172:        pub value: String,
src\md_elem\tree.rs:1179:    pub enum LinkReference {
src\md_elem\tree.rs:1229:    pub struct CodeOpts {
src\md_elem\tree.rs:1230:        pub language: String,
src\md_elem\tree.rs:1231:        pub metadata: Option<String>,
src\md_elem\tree.rs:1806:        pub fn empty() -> Self {
src\md_elem\tree.rs:1813:        pub fn with<S: Into<FootnoteId>>(mut self, footnote_id: S, body: Vec<MdElem>) -> Self {
src\md_elem\tree_ref.rs:9:        pub fn alignments(&self) -> &[Option<ColumnAlignment>] {
src\md_elem\tree_ref.rs:13:        pub fn rows(&self) -> &Vec<TableRow> {
src\md_elem\tree_ref.rs:23:        pub fn normalize(&mut self) {
src\md_elem\tree_ref.rs:40:        pub fn retain_columns_by_header<F, E>(&mut self, mut f: F) -> Result<(), E>
src\md_elem\tree_ref.rs:70:        pub fn retain_rows<F, E>(&mut self, mut f: F) -> Result<(), E>
src\md_elem\tree_ref.rs:87:        pub fn is_empty(&self) -> bool {
src\output\fmt_md.rs:14:pub struct MdWriterOptions {
src\output\fmt_md.rs:16:    pub link_reference_placement: ReferencePlacement,
src\output\fmt_md.rs:18:    pub footnote_reference_placement: ReferencePlacement,
src\output\fmt_md.rs:20:    pub inline_options: InlineElemOptions,
src\output\fmt_md.rs:35:    pub include_thematic_breaks: bool,
src\output\fmt_md.rs:53:    pub text_width: Option<usize>,
src\output\fmt_md.rs:58:pub enum ReferencePlacement {
src\output\fmt_md.rs:157:    pub fn write_one_md<W>(&mut self, out: &mut Output<W>, node_ref: &'md MdElem)
src\output\fmt_md.rs:531:pub mod tests {
src\output\fmt_md_inlines.rs:14:pub struct InlineElemOptions {
src\output\fmt_md_inlines.rs:15:    pub link_format: LinkTransform,
src\output\fmt_md_inlines.rs:16:    pub renumber_footnotes: bool,
src\output\fmt_md_inlines.rs:29:    pub links: HashMap<LinkLabel<'md>, UrlAndTitle<'md>>,
src\output\fmt_md_inlines.rs:30:    pub footnotes: HashSet<&'md FootnoteId>,
src\output\fmt_md_inlines.rs:44:    pub url: &'md String,
src\output\fmt_md_inlines.rs:46:    pub title: &'md Option<String>,
src\output\fmt_md_inlines.rs:76:    pub fn new(ctx: &'md MdContext, options: InlineElemOptions) -> Self {
src\output\fmt_md_inlines.rs:88:    pub fn has_pending_links(&self) -> bool {
src\output\fmt_md_inlines.rs:92:    pub fn has_pending_footnotes(&self) -> bool {
src\output\fmt_md_inlines.rs:96:    pub fn count_pending_links(&self) -> usize {
src\output\fmt_md_inlines.rs:100:    pub fn count_pending_footnotes(&self) -> usize {
src\output\fmt_md_inlines.rs:104:    pub fn drain_pending_links(&mut self) -> Vec<(LinkLabel<'md>, UrlAndTitle<'md>)> {
src\output\fmt_md_inlines.rs:108:    pub fn drain_pending_footnotes(&mut self) -> Vec<(String, &'md Vec<MdElem>)> {
src\output\fmt_md_inlines.rs:121:    pub fn write_line<I, W>(&mut self, out: &mut Output<W>, elems: I)
src\output\fmt_md_inlines.rs:131:    pub fn write_inline_element<W>(&mut self, out: &mut Output<W>, elem: &'md Inline)
src\output\fmt_md_inlines.rs:272:    pub fn write_linklike<W, L>(&mut self, out: &mut Output<W>, link_like: L)
src\output\fmt_md_inlines.rs:375:    pub fn write_url_title<W>(&mut self, out: &mut Output<W>, title: &Option<String>)
src\output\fmt_md_inlines.rs:418:    pub fn find_best_strategy(text: &str) -> Self {
src\output\fmt_plain_inline.rs:7:pub struct PlainWriterOptions {
src\output\fmt_plain_inline.rs:8:    pub include_breaks: bool,
src\output\fmt_plain_inline.rs:19:pub struct PlainWriter {
src\output\fmt_plain_inline.rs:25:    pub fn with_options(options: PlainWriterOptions) -> Self {
src\output\fmt_plain_inline.rs:30:    pub fn write<'md, I, W>(&self, nodes: I, out: &mut W)
src\output\fmt_plain_writer.rs:4:pub struct NewlineCollapser<W> {
src\output\fmt_plain_writer.rs:15:    pub fn new(underlying: W, max_newlines: usize) -> Self {
src\output\fmt_plain_writer.rs:23:    pub fn have_pending_newlines(&self) -> bool {
src\output\fmt_plain_writer.rs:30:    pub fn take_underlying(self) -> W {
src\output\footnote_transform.rs:4:pub struct FootnoteTransformer<'md> {
src\output\footnote_transform.rs:8:pub struct FootnoteTransformerToString<'a, 'md> {
src\output\footnote_transform.rs:14:    pub fn new(active: bool) -> Self {
src\output\footnote_transform.rs:20:    pub fn write<W>(&mut self, out: &mut Output<W>, label: &'md str)
src\output\footnote_transform.rs:34:    pub fn new_to_stringer<'a>(&'a mut self) -> FootnoteTransformerToString<'a, 'md> {
src\output\footnote_transform.rs:40:    pub fn transform(&mut self, label: &'md str) -> String {
src\output\link_transform.rs:13:pub enum LinkTransform {
src\output\link_transform.rs:39:    pub fn get_sort_string(&self, ctx: &'md MdContext) -> String {
src\output\link_transform.rs:127:    pub fn transform_variant(&self) -> LinkTransform {
src\output\mod.rs:14:pub use crate::output::fmt_md::*;
src\output\mod.rs:15:pub use crate::output::fmt_md_inlines::*;
src\output\mod.rs:16:pub use crate::output::link_transform::*;
src\output\mod.rs:17:pub use crate::output::output_adapter::*;
src\output\mod.rs:18:pub use crate::output::tree_ref_serde::*;
src\output\mod.rs:20:pub use crate::output::fmt_plain_inline::*;
src\output\output_adapter.rs:8:pub struct MdWriter {
src\output\output_adapter.rs:14:    pub fn with_options(options: MdWriterOptions) -> Self {
src\output\output_adapter.rs:19:    pub fn write<'md, I, W>(&self, ctx: &'md MdContext, nodes: I, out: &mut W)
src\output\output_adapter.rs:49:pub struct IoAdapter<W>(pub W);
src\output\tree_ref_serde.rs:12:pub struct SerializableMd<'md> {
src\output\tree_ref_serde.rs:152:    pub fn new(elems: &'md [MdElem], ctx: &'md MdContext, opts: InlineElemOptions) -> Self {
src\query\error.rs:11:pub struct ParseError {
src\query\error.rs:78:    pub fn to_string(&self, query_text: &str) -> String {
src\query\error.rs:100:pub struct DetachedSpan {
src\query\error.rs:101:    pub start: usize,
src\query\error.rs:102:    pub end: usize,
src\query\mod.rs:10:pub use error::*;
src\query\mod.rs:13:pub use pest::StringVariant;
src\query\pest.rs:2:pub use crate::query::pest::test_helpers::StringVariant;
src\query\pest.rs:12:pub struct Query {
src\query\pest.rs:20:pub struct Error {
src\query\pest.rs:54:    pub fn parse(query_text: &str) -> Result<Pairs, Error> {
src\query\pest.rs:117:    pub enum StringVariant {
src\query\pest.rs:125:        pub fn parse(self, query_text: &str) -> Result<(Pairs, &str), Error> {
src\query\pest.rs:134:        pub fn as_rule(self) -> Rule {
src\query\strings.rs:6:pub struct ParsedString {
src\query\strings.rs:7:    pub text: String,
src\query\strings.rs:8:    pub anchor_start: bool,
src\query\strings.rs:9:    pub anchor_end: bool,
src\query\strings.rs:10:    pub mode: ParsedStringMode,
src\query\strings.rs:11:    pub explicit_wildcard: bool,
src\query\strings.rs:12:    pub replace_string: Option<String>,
src\query\strings.rs:16:pub enum ParsedStringMode {
src\query\strings.rs:24:    pub fn is_equivalent_to_asterisk(&self) -> bool {
src\query\traversal.rs:3:pub type OnePair<'a> = OneOf<Pair<'a>>;
src\query\traversal.rs:6:pub trait PairMatcher {
src\query\traversal.rs:21:pub trait PairMatchStore<'a> {
src\query\traversal.rs:44:pub enum MatchStoreResult<'a> {
src\query\traversal.rs:50:pub struct Present(bool);
src\query\traversal.rs:53:    pub fn is_present(&self) -> bool {
src\query\traversal.rs:59:    pub fn store(&mut self, _pair: Pair) {
src\query\traversal.rs:65:pub struct OneOf<T>(Result<Option<T>, ()>);
src\query\traversal.rs:74:    pub fn take(self) -> Result<Option<T>, String> {
src\query\traversal.rs:78:    pub fn store(&mut self, item: T) {
src\query\traversal.rs:87:pub struct FindAll<'a, M>(M, Vec<Pair<'a>>);
src\query\traversal.rs:90:    pub fn new(matcher: M) -> Self {
src\query\traversal.rs:115:pub struct ByRule(Rule);
src\query\traversal.rs:118:    pub fn new(rule: Rule) -> Self {
src\query\traversal.rs:130:pub struct ByTag(&'static str);
src\query\traversal.rs:133:    pub fn new(tag: &'static str) -> Self {
src\query\traversal_composites.rs:27:        pub struct $finder_name {
src\query\traversal_composites.rs:34:        pub struct $result_name<'a> {
src\query\traversal_composites.rs:36:                pub $elem: $result,
src\query\traversal_composites.rs:51:            pub fn traverse(pairs: Pairs) -> $result_name {
src\run\cli.rs:13:            pub $name:ident : $ty:ty
src\run\cli.rs:19:        pub struct CliOptions {
src\run\cli.rs:78:        pub struct RunOptions {
src\run\cli.rs:81:            pub $name: $ty,
src\run\cli.rs:86:            pub add_breaks: Option<bool>,
src\run\cli.rs:88:            pub selectors: String,
src\run\cli.rs:90:            pub markdown_file_paths: Vec<String>
src\run\cli.rs:134:    pub link_pos: ReferencePlacement,
src\run\cli.rs:138:    pub footnote_pos: Option<ReferencePlacement>,
src\run\cli.rs:141:    pub link_format: LinkTransform,
src\run\cli.rs:144:    pub renumber_footnotes: bool,
src\run\cli.rs:148:    pub output: OutputFormat,
src\run\cli.rs:157:    pub wrap_width: Option<usize>,
src\run\cli.rs:161:    pub quiet: bool,
src\run\cli.rs:165:    pub allow_unknown_markdown: bool,
src\run\cli.rs:202:    pub fn should_add_breaks(&self) -> bool {
src\run\cli.rs:212:    pub fn extra_validation(&self) -> bool {
src\run\cli.rs:243:pub enum OutputFormat {
src\run\mod.rs:55:pub use cli::*;
src\run\mod.rs:56:pub use run_main::*;
src\run\run_main.rs:15:pub enum Error {
src\run\run_main.rs:37:pub struct QueryParseError {
src\run\run_main.rs:68:pub enum Input {
src\run\run_main.rs:122:pub trait OsFacade {
src\run\run_main.rs:172:pub fn run(cli: &RunOptions, os: &mut impl OsFacade) -> bool {
src\select\api.rs:31:pub struct SelectError {
src\select\api.rs:54:pub type Result<T> = std::result::Result<T, SelectError>;
src\select\matcher.rs:8:pub enum Matcher {
src\select\matcher.rs:30:pub struct Regex {
src\select\match_replace.rs:4:pub struct MatchReplace {
src\select\match_replace.rs:5:    pub matcher: Matcher,
src\select\match_replace.rs:6:    pub replacement: Option<String>,
src\select\match_selector.rs:8:pub trait MatchSelector<I> {
src\select\mod.rs:23:pub use crate::query::ParseError;
src\select\mod.rs:24:pub use api::{Result, SelectError};
src\select\mod.rs:25:pub use match_replace::*;
src\select\mod.rs:26:pub use matcher::*;
src\select\mod.rs:27:pub use selector::*;
src\select\selector.rs:8:pub enum ListItemTask {
src\select\selector.rs:21:pub struct ListItemMatcher {
src\select\selector.rs:23:    pub ordered: bool,
src\select\selector.rs:27:    pub task: ListItemTask,
src\select\selector.rs:28:    pub matcher: MatchReplace,
src\select\selector.rs:33:pub struct SectionMatcher {
src\select\selector.rs:34:    pub title: MatchReplace,
src\select\selector.rs:39:pub struct LinklikeMatcher {
src\select\selector.rs:40:    pub display_matcher: MatchReplace,
src\select\selector.rs:41:    pub url_matcher: MatchReplace,
src\select\selector.rs:46:pub struct BlockQuoteMatcher {
src\select\selector.rs:47:    pub text: MatchReplace,
src\select\selector.rs:52:pub struct HtmlMatcher {
src\select\selector.rs:53:    pub html: MatchReplace,
src\select\selector.rs:58:pub struct ParagraphMatcher {
src\select\selector.rs:59:    pub text: MatchReplace,
src\select\selector.rs:64:pub struct CodeBlockMatcher {
src\select\selector.rs:65:    pub language: MatchReplace,
src\select\selector.rs:66:    pub contents: MatchReplace,
src\select\selector.rs:71:pub struct FrontMatterMatcher {
src\select\selector.rs:72:    pub variant: Option<FrontMatterVariant>,
src\select\selector.rs:73:    pub text: MatchReplace,
src\select\selector.rs:78:pub struct TableMatcher {
src\select\selector.rs:79:    pub headers: MatchReplace,
src\select\selector.rs:80:    pub rows: MatchReplace,
src\select\selector.rs:86:pub enum Selector {
src\select\selector.rs:122:    pub fn find_nodes(self, doc: MdDoc) -> Result<(Vec<MdElem>, MdContext)> {
src\select\sel_chain.rs:5:pub struct ChainSelector {
src\select\sel_code_block.rs:7:pub struct CodeBlockSelector {
src\select\sel_link_like.rs:40:pub struct LinkSelector {
src\select\sel_link_like.rs:82:pub struct ImageSelector {
src\select\sel_link_like.rs:124:pub struct LinkMatchers {
src\select\sel_link_like.rs:125:    pub display_matcher: StringMatcher,
src\select\sel_link_like.rs:126:    pub url_matcher: StringMatcher,
src\select\sel_list_item.rs:7:pub struct ListItemSelector {
src\select\sel_list_item.rs:28:pub enum ListItemType {
src\select\sel_section.rs:7:pub struct SectionSelector {
src\select\sel_single_matcher.rs:11:            pub struct [<$name Selector>] {
src\select\sel_single_matcher.rs:36:pub struct HtmlSelector {
src\select\sel_single_matcher.rs:57:pub struct FrontMatterSelector {
src\select\sel_table.rs:7:pub struct TableSelector {
src\select\string_matcher.rs:9:pub struct StringMatcher {
src\select\string_matcher.rs:48:    pub fn to_select_error(&self, selector_name: &str) -> SelectError {
src\select\string_matcher.rs:64:    pub fn matches(&self, haystack: &str) -> Result<bool, StringMatchError> {
src\select\string_matcher.rs:90:    pub fn matches_inlines<I: Borrow<Inline>>(&self, haystack: &[I]) -> Result<bool, StringMatchError> {
src\select\string_matcher.rs:94:    pub fn matches_any<N: Borrow<MdElem>>(&self, haystacks: &[N]) -> Result<bool, StringMatchError> {
src\util\mod.rs:1:pub mod output;
src\util\mod.rs:2:pub mod str_utils;
src\util\mod.rs:3:pub mod utils_for_test;
src\util\mod.rs:4:pub mod vec_utils;
src\util\output.rs:5:pub trait SimpleWrite {
src\util\output.rs:21:pub struct Stream<W>(pub W);
src\util\output.rs:42:pub struct Output<W: SimpleWrite> {
src\util\output.rs:60:pub struct PreWriter<'a, W: SimpleWrite> {
src\util\output.rs:73:pub enum Block {
src\util\output.rs:102:    pub fn new(to: W, text_width: Option<usize>) -> Self {
src\util\output.rs:111:    pub fn without_text_wrapping(to: W) -> Self {
src\util\output.rs:115:    pub fn replace_underlying(&mut self, new: W) -> std::io::Result<W> {
src\util\output.rs:120:    pub fn with_block(&mut self, block: Block, action: impl FnOnce(&mut Self)) {
src\util\output.rs:132:    pub fn with_pre_block(&mut self, action: impl FnOnce(&mut PreWriter<W>)) {
src\util\output.rs:143:    pub fn without_wrapping(&mut self, action: impl FnOnce(&mut Self)) {
src\util\output.rs:171:    pub fn write_str(&mut self, text: &str) {
src\util\output.rs:175:    pub fn write_char(&mut self, ch: char) {
src\util\output.rs:220:    pub fn take_underlying(&mut self) -> std::io::Result<W> {
src\util\output.rs:440:    pub fn write_str(&mut self, text: &str) {
src\util\output.rs:444:    pub fn write_char(&mut self, ch: char) {
src\util\str_utils.rs:4:pub fn pad_to<W>(output: &mut Output<W>, input: &str, min_width: usize, alignment: Option<ColumnAlignment>)
src\util\str_utils.rs:33:pub struct CountingWriter<'a, W> {
src\util\str_utils.rs:39:    pub fn wrap(underlying: &'a mut W) -> Self {
src\util\str_utils.rs:50:    pub fn count(&self) -> usize {
src\util\utils_for_test.rs:14:        pub fn default_for_tests() -> Self {
src\util\utils_for_test.rs:20:        pub fn default_for_tests() -> Self {
src\util\utils_for_test.rs:26:        pub fn default_for_tests() -> Self {
src\util\utils_for_test.rs:39:        pub fn new_with<F>(init: F) -> Self
src\util\utils_for_test.rs:49:    pub fn get_only<T: Debug, C: IntoIterator<Item = T>>(col: C) -> T {
src\util\utils_for_test.rs:144:                pub struct [<VariantsChecker $name:lower:camel>] {
src\util\vec_utils.rs:3:pub struct IndexKeeper {
src\util\vec_utils.rs:8:    pub fn new() -> Self {
src\util\vec_utils.rs:14:    pub fn retain_when<I, F, E>(&mut self, items: &[I], mut allow_filter: F) -> Result<(), E>
src\util\vec_utils.rs:27:    pub fn retain_fn<I, E>(&self) -> impl FnMut(usize, &I) -> Result<bool, E> + '_ {
src\util\vec_utils.rs:43:    pub fn count_keeps(&self) -> usize {
src\util\vec_utils.rs:48:pub trait ItemRetainer<I> {
src\util\words_buffer.rs:3:pub enum WordBoundary {
src\util\words_buffer.rs:8:pub struct WordsBuffer {
src\util\words_buffer.rs:26:pub trait BufferedCharWrite: FnMut(char) -> usize {}
src\util\words_buffer.rs:30:pub struct WordBoundaryRestore {
src\util\words_buffer.rs:35:    pub fn restore_to(self, to: &mut WordsBuffer) {
src\util\words_buffer.rs:41:    pub fn new(line_length: usize) -> Self {
src\util\words_buffer.rs:56:    pub fn disabled() -> Self {
src\util\words_buffer.rs:63:    pub fn set_word_boundary(&mut self, boundary: WordBoundary) -> WordBoundaryRestore {
src\util\words_buffer.rs:69:    pub fn shorten_current_line(&mut self, by: usize) {
src\util\words_buffer.rs:73:    pub fn push(&mut self, ch: char, mut action: impl BufferedCharWrite) {
src\util\words_buffer.rs:136:    pub fn has_pending_word(&self) -> bool {
src\util\words_buffer.rs:144:    pub fn drain_pending_word(&mut self, mut drain_action: impl BufferedCharWrite) {
src\util\words_buffer.rs:157:    pub fn reset(&mut self) {
src\util\words_buffer.rs:406:        pub fn push(&mut self, ch: char) {
src\util\words_buffer.rs:414:        pub fn push_str(&mut self, text: &str) {
src\util\words_buffer.rs:418:        pub fn end(mut self) -> String {


